module.exports = "\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\nvar __esm = (fn, res) => function __init() {\n  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;\n};\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// <define:process>\nvar init_define_process = __esm({\n  \"<define:process>\"() {\n  }\n});\n\n// ../../node_modules/.pnpm/blob-polyfill@7.0.20220408/node_modules/blob-polyfill/Blob.js\nvar require_Blob = __commonJS({\n  \"../../node_modules/.pnpm/blob-polyfill@7.0.20220408/node_modules/blob-polyfill/Blob.js\"(exports2) {\n    \"use strict\";\n    init_define_process();\n    (function(global2) {\n      (function(factory) {\n        if (typeof define === \"function\" && define.amd) {\n          define([\"exports\"], factory);\n        } else if (typeof exports2 === \"object\" && typeof exports2.nodeName !== \"string\") {\n          factory(exports2);\n        } else {\n          factory(global2);\n        }\n      })(function(exports3) {\n        \"use strict\";\n        var BlobBuilder = global2.BlobBuilder || global2.WebKitBlobBuilder || global2.MSBlobBuilder || global2.MozBlobBuilder;\n        var URL = global2.URL || global2.webkitURL || function(href, a) {\n          a = document.createElement(\"a\");\n          a.href = href;\n          return a;\n        };\n        var origBlob = global2.Blob;\n        var createObjectURL = URL.createObjectURL;\n        var revokeObjectURL = URL.revokeObjectURL;\n        var strTag = global2.Symbol && global2.Symbol.toStringTag;\n        var blobSupported = false;\n        var blobSupportsArrayBufferView = false;\n        var blobBuilderSupported = BlobBuilder && BlobBuilder.prototype.append && BlobBuilder.prototype.getBlob;\n        try {\n          blobSupported = new Blob([\"\\xE4\"]).size === 2;\n          blobSupportsArrayBufferView = new Blob([new Uint8Array([1, 2])]).size === 2;\n        } catch (e) {\n        }\n        function mapArrayBufferViews(ary) {\n          return ary.map(function(chunk) {\n            if (chunk.buffer instanceof ArrayBuffer) {\n              var buf = chunk.buffer;\n              if (chunk.byteLength !== buf.byteLength) {\n                var copy = new Uint8Array(chunk.byteLength);\n                copy.set(new Uint8Array(buf, chunk.byteOffset, chunk.byteLength));\n                buf = copy.buffer;\n              }\n              return buf;\n            }\n            return chunk;\n          });\n        }\n        __name(mapArrayBufferViews, \"mapArrayBufferViews\");\n        function BlobBuilderConstructor(ary, options) {\n          options = options || {};\n          var bb = new BlobBuilder();\n          mapArrayBufferViews(ary).forEach(function(part) {\n            bb.append(part);\n          });\n          return options.type ? bb.getBlob(options.type) : bb.getBlob();\n        }\n        __name(BlobBuilderConstructor, \"BlobBuilderConstructor\");\n        function BlobConstructor(ary, options) {\n          return new origBlob(mapArrayBufferViews(ary), options || {});\n        }\n        __name(BlobConstructor, \"BlobConstructor\");\n        if (global2.Blob) {\n          BlobBuilderConstructor.prototype = Blob.prototype;\n          BlobConstructor.prototype = Blob.prototype;\n        }\n        function stringEncode(string) {\n          var pos = 0;\n          var len = string.length;\n          var Arr = global2.Uint8Array || Array;\n          var at = 0;\n          var tlen = Math.max(32, len + (len >> 1) + 7);\n          var target = new Arr(tlen >> 3 << 3);\n          while (pos < len) {\n            var value = string.charCodeAt(pos++);\n            if (value >= 55296 && value <= 56319) {\n              if (pos < len) {\n                var extra = string.charCodeAt(pos);\n                if ((extra & 64512) === 56320) {\n                  ++pos;\n                  value = ((value & 1023) << 10) + (extra & 1023) + 65536;\n                }\n              }\n              if (value >= 55296 && value <= 56319) {\n                continue;\n              }\n            }\n            if (at + 4 > target.length) {\n              tlen += 8;\n              tlen *= 1 + pos / string.length * 2;\n              tlen = tlen >> 3 << 3;\n              var update = new Uint8Array(tlen);\n              update.set(target);\n              target = update;\n            }\n            if ((value & 4294967168) === 0) {\n              target[at++] = value;\n              continue;\n            } else if ((value & 4294965248) === 0) {\n              target[at++] = value >> 6 & 31 | 192;\n            } else if ((value & 4294901760) === 0) {\n              target[at++] = value >> 12 & 15 | 224;\n              target[at++] = value >> 6 & 63 | 128;\n            } else if ((value & 4292870144) === 0) {\n              target[at++] = value >> 18 & 7 | 240;\n              target[at++] = value >> 12 & 63 | 128;\n              target[at++] = value >> 6 & 63 | 128;\n            } else {\n              continue;\n            }\n            target[at++] = value & 63 | 128;\n          }\n          return target.slice(0, at);\n        }\n        __name(stringEncode, \"stringEncode\");\n        function stringDecode(buf) {\n          var end = buf.length;\n          var res = [];\n          var i = 0;\n          while (i < end) {\n            var firstByte = buf[i];\n            var codePoint = null;\n            var bytesPerSequence = firstByte > 239 ? 4 : firstByte > 223 ? 3 : firstByte > 191 ? 2 : 1;\n            if (i + bytesPerSequence <= end) {\n              var secondByte, thirdByte, fourthByte, tempCodePoint;\n              switch (bytesPerSequence) {\n                case 1:\n                  if (firstByte < 128) {\n                    codePoint = firstByte;\n                  }\n                  break;\n                case 2:\n                  secondByte = buf[i + 1];\n                  if ((secondByte & 192) === 128) {\n                    tempCodePoint = (firstByte & 31) << 6 | secondByte & 63;\n                    if (tempCodePoint > 127) {\n                      codePoint = tempCodePoint;\n                    }\n                  }\n                  break;\n                case 3:\n                  secondByte = buf[i + 1];\n                  thirdByte = buf[i + 2];\n                  if ((secondByte & 192) === 128 && (thirdByte & 192) === 128) {\n                    tempCodePoint = (firstByte & 15) << 12 | (secondByte & 63) << 6 | thirdByte & 63;\n                    if (tempCodePoint > 2047 && (tempCodePoint < 55296 || tempCodePoint > 57343)) {\n                      codePoint = tempCodePoint;\n                    }\n                  }\n                  break;\n                case 4:\n                  secondByte = buf[i + 1];\n                  thirdByte = buf[i + 2];\n                  fourthByte = buf[i + 3];\n                  if ((secondByte & 192) === 128 && (thirdByte & 192) === 128 && (fourthByte & 192) === 128) {\n                    tempCodePoint = (firstByte & 15) << 18 | (secondByte & 63) << 12 | (thirdByte & 63) << 6 | fourthByte & 63;\n                    if (tempCodePoint > 65535 && tempCodePoint < 1114112) {\n                      codePoint = tempCodePoint;\n                    }\n                  }\n              }\n            }\n            if (codePoint === null) {\n              codePoint = 65533;\n              bytesPerSequence = 1;\n            } else if (codePoint > 65535) {\n              codePoint -= 65536;\n              res.push(codePoint >>> 10 & 1023 | 55296);\n              codePoint = 56320 | codePoint & 1023;\n            }\n            res.push(codePoint);\n            i += bytesPerSequence;\n          }\n          var len = res.length;\n          var str = \"\";\n          var j = 0;\n          while (j < len) {\n            str += String.fromCharCode.apply(String, res.slice(j, j += 4096));\n          }\n          return str;\n        }\n        __name(stringDecode, \"stringDecode\");\n        var textEncode = typeof TextEncoder === \"function\" ? TextEncoder.prototype.encode.bind(new TextEncoder()) : stringEncode;\n        var textDecode = typeof TextDecoder === \"function\" ? TextDecoder.prototype.decode.bind(new TextDecoder()) : stringDecode;\n        function FakeBlobBuilder() {\n          function bufferClone(buf) {\n            var view = new Array(buf.byteLength);\n            var array = new Uint8Array(buf);\n            var i = view.length;\n            while (i--) {\n              view[i] = array[i];\n            }\n            return view;\n          }\n          __name(bufferClone, \"bufferClone\");\n          function array2base64(input) {\n            var byteToCharMap = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n            var output = [];\n            for (var i = 0; i < input.length; i += 3) {\n              var byte1 = input[i];\n              var haveByte2 = i + 1 < input.length;\n              var byte2 = haveByte2 ? input[i + 1] : 0;\n              var haveByte3 = i + 2 < input.length;\n              var byte3 = haveByte3 ? input[i + 2] : 0;\n              var outByte1 = byte1 >> 2;\n              var outByte2 = (byte1 & 3) << 4 | byte2 >> 4;\n              var outByte3 = (byte2 & 15) << 2 | byte3 >> 6;\n              var outByte4 = byte3 & 63;\n              if (!haveByte3) {\n                outByte4 = 64;\n                if (!haveByte2) {\n                  outByte3 = 64;\n                }\n              }\n              output.push(\n                byteToCharMap[outByte1],\n                byteToCharMap[outByte2],\n                byteToCharMap[outByte3],\n                byteToCharMap[outByte4]\n              );\n            }\n            return output.join(\"\");\n          }\n          __name(array2base64, \"array2base64\");\n          var create = Object.create || function(a) {\n            function c() {\n            }\n            __name(c, \"c\");\n            c.prototype = a;\n            return new c();\n          };\n          function getObjectTypeName(o) {\n            return Object.prototype.toString.call(o).slice(8, -1);\n          }\n          __name(getObjectTypeName, \"getObjectTypeName\");\n          function isPrototypeOf(c, o) {\n            return typeof c === \"object\" && Object.prototype.isPrototypeOf.call(c.prototype, o);\n          }\n          __name(isPrototypeOf, \"isPrototypeOf\");\n          function isDataView(o) {\n            return getObjectTypeName(o) === \"DataView\" || isPrototypeOf(global2.DataView, o);\n          }\n          __name(isDataView, \"isDataView\");\n          var arrayBufferClassNames = [\n            \"Int8Array\",\n            \"Uint8Array\",\n            \"Uint8ClampedArray\",\n            \"Int16Array\",\n            \"Uint16Array\",\n            \"Int32Array\",\n            \"Uint32Array\",\n            \"Float32Array\",\n            \"Float64Array\",\n            \"ArrayBuffer\"\n          ];\n          function includes(a, v) {\n            return a.indexOf(v) !== -1;\n          }\n          __name(includes, \"includes\");\n          function isArrayBuffer(o) {\n            return includes(arrayBufferClassNames, getObjectTypeName(o)) || isPrototypeOf(global2.ArrayBuffer, o);\n          }\n          __name(isArrayBuffer, \"isArrayBuffer\");\n          function concatTypedarrays(chunks) {\n            var size = 0;\n            var j = chunks.length;\n            while (j--) {\n              size += chunks[j].length;\n            }\n            var b = new Uint8Array(size);\n            var offset = 0;\n            for (var i = 0; i < chunks.length; i++) {\n              var chunk = chunks[i];\n              b.set(chunk, offset);\n              offset += chunk.byteLength || chunk.length;\n            }\n            return b;\n          }\n          __name(concatTypedarrays, \"concatTypedarrays\");\n          function Blob3(chunks, opts) {\n            chunks = chunks ? chunks.slice() : [];\n            opts = opts == null ? {} : opts;\n            for (var i = 0, len = chunks.length; i < len; i++) {\n              var chunk = chunks[i];\n              if (chunk instanceof Blob3) {\n                chunks[i] = chunk._buffer;\n              } else if (typeof chunk === \"string\") {\n                chunks[i] = textEncode(chunk);\n              } else if (isDataView(chunk)) {\n                chunks[i] = bufferClone(chunk.buffer);\n              } else if (isArrayBuffer(chunk)) {\n                chunks[i] = bufferClone(chunk);\n              } else {\n                chunks[i] = textEncode(String(chunk));\n              }\n            }\n            this._buffer = global2.Uint8Array ? concatTypedarrays(chunks) : [].concat.apply([], chunks);\n            this.size = this._buffer.length;\n            this.type = opts.type || \"\";\n            if (/[^\\u0020-\\u007E]/.test(this.type)) {\n              this.type = \"\";\n            } else {\n              this.type = this.type.toLowerCase();\n            }\n          }\n          __name(Blob3, \"Blob\");\n          Blob3.prototype.arrayBuffer = function() {\n            return Promise.resolve(this._buffer.buffer || this._buffer);\n          };\n          Blob3.prototype.text = function() {\n            return Promise.resolve(textDecode(this._buffer));\n          };\n          Blob3.prototype.slice = function(start, end, type) {\n            var slice = this._buffer.slice(start || 0, end || this._buffer.length);\n            return new Blob3([slice], { type });\n          };\n          Blob3.prototype.toString = function() {\n            return \"[object Blob]\";\n          };\n          function File2(chunks, name, opts) {\n            opts = opts || {};\n            var a = Blob3.call(this, chunks, opts) || this;\n            a.name = name.replace(/\\//g, \":\");\n            a.lastModifiedDate = opts.lastModified ? new Date(opts.lastModified) : /* @__PURE__ */ new Date();\n            a.lastModified = +a.lastModifiedDate;\n            return a;\n          }\n          __name(File2, \"File\");\n          File2.prototype = create(Blob3.prototype);\n          File2.prototype.constructor = File2;\n          if (Object.setPrototypeOf) {\n            Object.setPrototypeOf(File2, Blob3);\n          } else {\n            try {\n              File2.__proto__ = Blob3;\n            } catch (e) {\n            }\n          }\n          File2.prototype.toString = function() {\n            return \"[object File]\";\n          };\n          function FileReader2() {\n            if (!(this instanceof FileReader2)) {\n              throw new TypeError(\"Failed to construct 'FileReader': Please use the 'new' operator, this DOM object constructor cannot be called as a function.\");\n            }\n            var delegate = document.createDocumentFragment();\n            this.addEventListener = delegate.addEventListener;\n            this.dispatchEvent = function(evt) {\n              var local = this[\"on\" + evt.type];\n              if (typeof local === \"function\")\n                local(evt);\n              delegate.dispatchEvent(evt);\n            };\n            this.removeEventListener = delegate.removeEventListener;\n          }\n          __name(FileReader2, \"FileReader\");\n          function _read(fr, blob2, kind) {\n            if (!(blob2 instanceof Blob3)) {\n              throw new TypeError(\"Failed to execute '\" + kind + \"' on 'FileReader': parameter 1 is not of type 'Blob'.\");\n            }\n            fr.result = \"\";\n            setTimeout(function() {\n              this.readyState = FileReader2.LOADING;\n              fr.dispatchEvent(new Event(\"load\"));\n              fr.dispatchEvent(new Event(\"loadend\"));\n            });\n          }\n          __name(_read, \"_read\");\n          FileReader2.EMPTY = 0;\n          FileReader2.LOADING = 1;\n          FileReader2.DONE = 2;\n          FileReader2.prototype.error = null;\n          FileReader2.prototype.onabort = null;\n          FileReader2.prototype.onerror = null;\n          FileReader2.prototype.onload = null;\n          FileReader2.prototype.onloadend = null;\n          FileReader2.prototype.onloadstart = null;\n          FileReader2.prototype.onprogress = null;\n          FileReader2.prototype.readAsDataURL = function(blob2) {\n            _read(this, blob2, \"readAsDataURL\");\n            this.result = \"data:\" + blob2.type + \";base64,\" + array2base64(blob2._buffer);\n          };\n          FileReader2.prototype.readAsText = function(blob2) {\n            _read(this, blob2, \"readAsText\");\n            this.result = textDecode(blob2._buffer);\n          };\n          FileReader2.prototype.readAsArrayBuffer = function(blob2) {\n            _read(this, blob2, \"readAsText\");\n            this.result = (blob2._buffer.buffer || blob2._buffer).slice();\n          };\n          FileReader2.prototype.abort = function() {\n          };\n          URL.createObjectURL = function(blob2) {\n            return blob2 instanceof Blob3 ? \"data:\" + blob2.type + \";base64,\" + array2base64(blob2._buffer) : createObjectURL.call(URL, blob2);\n          };\n          URL.revokeObjectURL = function(url) {\n            revokeObjectURL && revokeObjectURL.call(URL, url);\n          };\n          var _send = global2.XMLHttpRequest && global2.XMLHttpRequest.prototype.send;\n          if (_send) {\n            XMLHttpRequest.prototype.send = function(data) {\n              if (data instanceof Blob3) {\n                this.setRequestHeader(\"Content-Type\", data.type);\n                _send.call(this, textDecode(data._buffer));\n              } else {\n                _send.call(this, data);\n              }\n            };\n          }\n          exports3.Blob = Blob3;\n          exports3.File = File2;\n          exports3.FileReader = FileReader2;\n          exports3.URL = URL;\n        }\n        __name(FakeBlobBuilder, \"FakeBlobBuilder\");\n        function fixFileAndXHR() {\n          var isIE = !!global2.ActiveXObject || \"-ms-scroll-limit\" in document.documentElement.style && \"-ms-ime-align\" in document.documentElement.style;\n          var _send = global2.XMLHttpRequest && global2.XMLHttpRequest.prototype.send;\n          if (isIE && _send) {\n            XMLHttpRequest.prototype.send = function(data) {\n              if (data instanceof Blob) {\n                this.setRequestHeader(\"Content-Type\", data.type);\n                _send.call(this, data);\n              } else {\n                _send.call(this, data);\n              }\n            };\n          }\n          try {\n            new File([], \"\");\n            exports3.File = global2.File;\n            exports3.FileReader = global2.FileReader;\n          } catch (e) {\n            try {\n              exports3.File = new Function(\n                'class File extends Blob {constructor(chunks, name, opts) {opts = opts || {};super(chunks, opts || {});this.name = name.replace(/\\\\//g, \":\");this.lastModifiedDate = opts.lastModified ? new Date(opts.lastModified) : new Date();this.lastModified = +this.lastModifiedDate;}};return new File([], \"\"), File'\n              )();\n            } catch (e2) {\n              exports3.File = function(b, d, c) {\n                var blob2 = new Blob(b, c);\n                var t = c && void 0 !== c.lastModified ? new Date(c.lastModified) : /* @__PURE__ */ new Date();\n                blob2.name = d.replace(/\\//g, \":\");\n                blob2.lastModifiedDate = t;\n                blob2.lastModified = +t;\n                blob2.toString = function() {\n                  return \"[object File]\";\n                };\n                if (strTag) {\n                  blob2[strTag] = \"File\";\n                }\n                return blob2;\n              };\n            }\n          }\n        }\n        __name(fixFileAndXHR, \"fixFileAndXHR\");\n        if (blobSupported) {\n          fixFileAndXHR();\n          exports3.Blob = blobSupportsArrayBufferView ? global2.Blob : BlobConstructor;\n        } else if (blobBuilderSupported) {\n          fixFileAndXHR();\n          exports3.Blob = BlobBuilderConstructor;\n        } else {\n          FakeBlobBuilder();\n        }\n        if (strTag) {\n          if (!exports3.File.prototype[strTag])\n            exports3.File.prototype[strTag] = \"File\";\n          if (!exports3.Blob.prototype[strTag])\n            exports3.Blob.prototype[strTag] = \"Blob\";\n          if (!exports3.FileReader.prototype[strTag])\n            exports3.FileReader.prototype[strTag] = \"FileReader\";\n        }\n        var blob = exports3.Blob.prototype;\n        var stream;\n        try {\n          new ReadableStream({ type: \"bytes\" });\n          stream = /* @__PURE__ */ __name(function stream2() {\n            var position = 0;\n            var blob2 = this;\n            return new ReadableStream({\n              type: \"bytes\",\n              autoAllocateChunkSize: 524288,\n              pull: function(controller) {\n                var v = controller.byobRequest.view;\n                var chunk = blob2.slice(position, position + v.byteLength);\n                return chunk.arrayBuffer().then(function(buffer) {\n                  var uint8array = new Uint8Array(buffer);\n                  var bytesRead = uint8array.byteLength;\n                  position += bytesRead;\n                  v.set(uint8array);\n                  controller.byobRequest.respond(bytesRead);\n                  if (position >= blob2.size)\n                    controller.close();\n                });\n              }\n            });\n          }, \"stream\");\n        } catch (e) {\n          try {\n            new ReadableStream({});\n            stream = /* @__PURE__ */ __name(function stream2(blob2) {\n              var position = 0;\n              return new ReadableStream({\n                pull: function(controller) {\n                  var chunk = blob2.slice(position, position + 524288);\n                  return chunk.arrayBuffer().then(function(buffer) {\n                    position += buffer.byteLength;\n                    var uint8array = new Uint8Array(buffer);\n                    controller.enqueue(uint8array);\n                    if (position == blob2.size)\n                      controller.close();\n                  });\n                }\n              });\n            }, \"stream\");\n          } catch (e2) {\n            try {\n              new Response(\"\").body.getReader().read();\n              stream = /* @__PURE__ */ __name(function stream2() {\n                return new Response(this).body;\n              }, \"stream\");\n            } catch (e3) {\n              stream = /* @__PURE__ */ __name(function stream2() {\n                throw new Error(\"Include https://github.com/MattiasBuelens/web-streams-polyfill\");\n              }, \"stream\");\n            }\n          }\n        }\n        function promisify(obj) {\n          return new Promise(function(resolve, reject) {\n            obj.onload = obj.onerror = function(evt) {\n              obj.onload = obj.onerror = null;\n              evt.type === \"load\" ? resolve(obj.result || obj) : reject(new Error(\"Failed to read the blob/file\"));\n            };\n          });\n        }\n        __name(promisify, \"promisify\");\n        if (!blob.arrayBuffer) {\n          blob.arrayBuffer = /* @__PURE__ */ __name(function arrayBuffer() {\n            var fr = new FileReader();\n            fr.readAsArrayBuffer(this);\n            return promisify(fr);\n          }, \"arrayBuffer\");\n        }\n        if (!blob.text) {\n          blob.text = /* @__PURE__ */ __name(function text() {\n            var fr = new FileReader();\n            fr.readAsText(this);\n            return promisify(fr);\n          }, \"text\");\n        }\n        if (!blob.stream) {\n          blob.stream = stream;\n        }\n      });\n    })(\n      typeof self !== \"undefined\" && self || typeof window !== \"undefined\" && window || typeof global !== \"undefined\" && global || exports2\n    );\n  }\n});\n\n// src/primitives/blob.js\nvar blob_exports = {};\n__export(blob_exports, {\n  Blob: () => import_blob_polyfill.Blob\n});\nmodule.exports = __toCommonJS(blob_exports);\ninit_define_process();\nvar import_blob_polyfill = __toESM(require_Blob());\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  Blob\n});\n"