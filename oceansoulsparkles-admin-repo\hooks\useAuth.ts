import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

interface User {
  id: string;
  email: string;
  role: 'DEV' | 'Admin' | 'Artist' | 'Braider';
  firstName: string;
  lastName: string;
  isActive: boolean;
  mfaEnabled: boolean;
  lastActivity: number;
  permissions: string[];
}

interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

export function useAuth() {
  const router = useRouter();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null
  });

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      
      if (!token) {
        setAuthState({ user: null, loading: false, error: null });
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        }
      });

      if (!response.ok) {
        // Token is invalid
        localStorage.removeItem('admin-token');
        setAuthState({ user: null, loading: false, error: 'Session expired' });
        return;
      }

      const data = await response.json();
      setAuthState({ 
        user: data.user, 
        loading: false, 
        error: null 
      });

    } catch (error) {
      console.error('Auth check error:', error);
      localStorage.removeItem('admin-token');
      setAuthState({ 
        user: null, 
        loading: false, 
        error: 'Authentication failed' 
      });
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Login failed');
      }

      if (data.requiresMFA) {
        return { requiresMFA: true, user: data.user };
      }

      localStorage.setItem('admin-token', data.token);
      setAuthState({ 
        user: data.user, 
        loading: false, 
        error: null 
      });

      return { success: true, user: data.user };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setAuthState(prev => ({ 
        ...prev, 
        loading: false, 
        error: errorMessage 
      }));
      throw error;
    }
  };

  const verifyMFA = async (userId: string, mfaCode: string) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));

      const response = await fetch('/api/auth/mfa-verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userId, mfaCode })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'MFA verification failed');
      }

      localStorage.setItem('admin-token', data.token);
      setAuthState({ 
        user: data.user, 
        loading: false, 
        error: null 
      });

      return { success: true, user: data.user };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'MFA verification failed';
      setAuthState(prev => ({ 
        ...prev, 
        loading: false, 
        error: errorMessage 
      }));
      throw error;
    }
  };

  const logout = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      
      if (token) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('admin-token');
      setAuthState({ user: null, loading: false, error: null });
      router.push('/admin/login');
    }
  };

  const updateUser = (updatedUser: Partial<User>) => {
    setAuthState(prev => ({
      ...prev,
      user: prev.user ? { ...prev.user, ...updatedUser } : null
    }));
  };

  const hasPermission = (permission: string): boolean => {
    if (!authState.user) return false;
    
    // DEV role has all permissions
    if (authState.user.role === 'DEV') return true;
    
    // Check specific permissions
    return authState.user.permissions.includes(permission);
  };

  const hasRole = (roles: string | string[]): boolean => {
    if (!authState.user) return false;
    
    const roleArray = Array.isArray(roles) ? roles : [roles];
    return roleArray.includes(authState.user.role);
  };

  const isAdmin = (): boolean => {
    return hasRole(['DEV', 'Admin']);
  };

  const isStaff = (): boolean => {
    return hasRole(['DEV', 'Admin', 'Artist', 'Braider']);
  };

  return {
    user: authState.user,
    loading: authState.loading,
    error: authState.error,
    login,
    verifyMFA,
    logout,
    updateUser,
    hasPermission,
    hasRole,
    isAdmin,
    isStaff,
    checkAuth
  };
}
