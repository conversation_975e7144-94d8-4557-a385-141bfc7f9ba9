import type { NextRequest } from 'next/server';

interface RateLimitResult {
  allowed: boolean;
  ip: string;
  remaining?: number;
  resetTime?: number;
  reason?: string;
}

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

// In-memory store for rate limiting (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Different rate limits for different endpoints
const RATE_LIMITS: Record<string, RateLimitConfig> = {
  '/api/auth/login': {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
  },
  '/api/auth/mfa': {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 10, // 10 attempts per 5 minutes
  },
  '/api/auth/forgot-password': {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3, // 3 attempts per hour
  },
  '/api/admin': {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
  },
  'default': {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 requests per minute
  }
};

/**
 * Check rate limit for incoming request
 */
export async function rateLimitCheck(request: NextRequest): Promise<RateLimitResult> {
  const ip = getClientIP(request);
  const path = request.nextUrl.pathname;
  
  // Get rate limit config for this path
  const config = getRateLimitConfig(path);
  
  // Create key for this IP and path combination
  const key = `${ip}:${path}`;
  
  const now = Date.now();
  const windowStart = now - config.windowMs;
  
  // Get current count for this key
  let record = rateLimitStore.get(key);
  
  // Clean up expired records
  if (record && record.resetTime < now) {
    rateLimitStore.delete(key);
    record = undefined;
  }
  
  // Initialize record if it doesn't exist
  if (!record) {
    record = {
      count: 0,
      resetTime: now + config.windowMs
    };
  }
  
  // Check if limit exceeded
  if (record.count >= config.maxRequests) {
    return {
      allowed: false,
      ip,
      remaining: 0,
      resetTime: record.resetTime,
      reason: 'Rate limit exceeded'
    };
  }
  
  // Increment count
  record.count++;
  rateLimitStore.set(key, record);
  
  // Clean up old entries periodically
  if (Math.random() < 0.01) { // 1% chance
    cleanupExpiredEntries();
  }
  
  return {
    allowed: true,
    ip,
    remaining: config.maxRequests - record.count,
    resetTime: record.resetTime
  };
}

/**
 * Get rate limit configuration for a specific path
 */
function getRateLimitConfig(path: string): RateLimitConfig {
  // Check for exact matches first
  if (RATE_LIMITS[path]) {
    return RATE_LIMITS[path];
  }
  
  // Check for pattern matches
  for (const [pattern, config] of Object.entries(RATE_LIMITS)) {
    if (pattern !== 'default' && path.startsWith(pattern)) {
      return config;
    }
  }
  
  // Return default config
  return RATE_LIMITS.default;
}

/**
 * Get client IP address from request
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return request.ip || 'unknown';
}

/**
 * Clean up expired entries from rate limit store
 */
function cleanupExpiredEntries() {
  const now = Date.now();
  
  const keysToDelete: string[] = [];
  rateLimitStore.forEach((record, key) => {
    if (record.resetTime < now) {
      keysToDelete.push(key);
    }
  });

  keysToDelete.forEach(key => rateLimitStore.delete(key));
}

/**
 * Reset rate limit for specific IP (admin function)
 */
export async function resetRateLimit(ip: string, path?: string): Promise<boolean> {
  try {
    if (path) {
      const key = `${ip}:${path}`;
      rateLimitStore.delete(key);
    } else {
      // Reset all limits for this IP
      const keysToReset: string[] = [];
      rateLimitStore.forEach((_, key) => {
        if (key.startsWith(`${ip}:`)) {
          keysToReset.push(key);
        }
      });
      keysToReset.forEach(key => rateLimitStore.delete(key));
    }
    
    console.log(`Rate limit reset for IP: ${ip}${path ? ` on path: ${path}` : ''}`);
    return true;
  } catch (error) {
    console.error('Error resetting rate limit:', error);
    return false;
  }
}

/**
 * Get current rate limit status for IP
 */
export async function getRateLimitStatus(ip: string, path: string): Promise<{
  count: number;
  limit: number;
  remaining: number;
  resetTime: number;
}> {
  const config = getRateLimitConfig(path);
  const key = `${ip}:${path}`;
  const record = rateLimitStore.get(key);
  
  if (!record || record.resetTime < Date.now()) {
    return {
      count: 0,
      limit: config.maxRequests,
      remaining: config.maxRequests,
      resetTime: Date.now() + config.windowMs
    };
  }
  
  return {
    count: record.count,
    limit: config.maxRequests,
    remaining: Math.max(0, config.maxRequests - record.count),
    resetTime: record.resetTime
  };
}

/**
 * Add custom rate limit for specific endpoint
 */
export function addRateLimit(path: string, config: RateLimitConfig): void {
  RATE_LIMITS[path] = config;
}

/**
 * Remove rate limit for specific endpoint
 */
export function removeRateLimit(path: string): boolean {
  if (path === 'default') {
    return false; // Cannot remove default
  }
  
  return delete RATE_LIMITS[path];
}

/**
 * Get all current rate limit configurations
 */
export function getAllRateLimits(): Record<string, RateLimitConfig> {
  return { ...RATE_LIMITS };
}

/**
 * Advanced rate limiting with different strategies
 */
export class AdvancedRateLimiter {
  private store = new Map<string, any>();
  
  /**
   * Sliding window rate limiter
   */
  async slidingWindow(
    key: string, 
    windowMs: number, 
    maxRequests: number
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    let requests = this.store.get(key) || [];
    
    // Remove old requests outside the window
    requests = requests.filter((timestamp: number) => timestamp > windowStart);
    
    if (requests.length >= maxRequests) {
      return {
        allowed: false,
        ip: key,
        remaining: 0,
        resetTime: requests[0] + windowMs,
        reason: 'Sliding window rate limit exceeded'
      };
    }
    
    // Add current request
    requests.push(now);
    this.store.set(key, requests);
    
    return {
      allowed: true,
      ip: key,
      remaining: maxRequests - requests.length,
      resetTime: now + windowMs
    };
  }
  
  /**
   * Token bucket rate limiter
   */
  async tokenBucket(
    key: string,
    capacity: number,
    refillRate: number,
    tokensRequested: number = 1
  ): Promise<RateLimitResult> {
    const now = Date.now();
    let bucket = this.store.get(key) || {
      tokens: capacity,
      lastRefill: now
    };
    
    // Calculate tokens to add based on time elapsed
    const timePassed = now - bucket.lastRefill;
    const tokensToAdd = Math.floor(timePassed * refillRate / 1000);
    
    bucket.tokens = Math.min(capacity, bucket.tokens + tokensToAdd);
    bucket.lastRefill = now;
    
    if (bucket.tokens < tokensRequested) {
      this.store.set(key, bucket);
      return {
        allowed: false,
        ip: key,
        remaining: bucket.tokens,
        reason: 'Token bucket exhausted'
      };
    }
    
    bucket.tokens -= tokensRequested;
    this.store.set(key, bucket);
    
    return {
      allowed: true,
      ip: key,
      remaining: bucket.tokens
    };
  }
}
