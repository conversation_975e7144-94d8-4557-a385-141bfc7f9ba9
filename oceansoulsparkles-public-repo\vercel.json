{"version": 2, "name": "oceansoulsparkles-public", "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/(.*)", "dest": "/$1"}], "env": {"NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key", "NEXT_PUBLIC_SITE_URL": "@next_public_site_url", "NEXT_PUBLIC_SQUARE_APPLICATION_ID": "@next_public_square_application_id", "NEXT_PUBLIC_SQUARE_LOCATION_ID": "@next_public_square_location_id", "NEXT_PUBLIC_GOOGLE_ANALYTICS_ID": "@next_public_google_analytics_id", "NEXT_PUBLIC_ONESIGNAL_APP_ID": "@next_public_onesignal_app_id", "NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID": "@next_public_onesignal_safari_web_id", "NEXT_PUBLIC_ADMIN_ACCESS": "false", "NEXT_PUBLIC_DEV_MODE": "false", "NEXT_PUBLIC_DEBUG_AUTH": "false", "ENABLE_AUTH_BYPASS": "false"}, "functions": {"pages/api/**/*.js": {"maxDuration": 10}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(self), payment=(self)"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/admin/(.*)", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow"}]}, {"source": "/api/admin/(.*)", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow"}]}], "redirects": [{"source": "/admin/:path*", "destination": "/404", "permanent": false}, {"source": "/staff/:path*", "destination": "/404", "permanent": false}, {"source": "/artist/:path*", "destination": "/404", "permanent": false}, {"source": "/apply/:path*", "destination": "/404", "permanent": false}], "rewrites": [{"source": "/.well-known/apple-developer-merchantid-domain-association", "destination": "/api/apple-pay/domain-association"}]}