import '@/styles/globals.css'
import 'react-toastify/dist/ReactToastify.css'
import Head from 'next/head'
import React, { useEffect, memo } from 'react'
import { CustomerProvider } from '@/contexts/CustomerContext'
import PWAProvider from '@/components/PWAProvider'
import { ToastContainer } from 'react-toastify'
import ErrorBoundary from '@/components/ErrorBoundary'

// Memoized Head component for performance
const MemoizedHead = memo(function MemoizedHead() {
  return (
    <Head>
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/manifest.json" />
      <meta name="theme-color" content="#3788d8" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="Ocean Soul Sparkles" />
      
      {/* Security headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      
      {/* SEO and Social Media */}
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content="Ocean Soul Sparkles" />
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@oceansoulsparkles" />
      
      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://ndlgbcsbidyhxbpqzgqp.supabase.co" />
      <link rel="preconnect" href="https://js.squareup.com" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
    </Head>
  );
});

// Memoized ToastContainer for performance
const MemoizedToastContainer = memo(function MemoizedToastContainer() {
  return (
    <ToastContainer
      position="top-right"
      autoClose={5000}
      hideProgressBar={false}
      newestOnTop={false}
      closeOnClick
      rtl={false}
      pauseOnFocusLoss
      draggable
      pauseOnHover
      theme="light"
      toastClassName="custom-toast"
      bodyClassName="custom-toast-body"
      progressClassName="custom-toast-progress"
    />
  );
});

function MyApp({ Component, pageProps, router }) {
  // Initialize public-only features
  useEffect(() => {
    // Initialize production security for public site
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DISABLE_CONSOLE_LOGS === 'true') {
      // Disable console logging in production for security
      console.log = () => {};
      console.warn = () => {};
      console.error = () => {};
    }

    // Initialize OneSignal for customer notifications (if enabled)
    if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID) {
      import('@/lib/onesignal-public').then(({ initializeOneSignalPublic }) => {
        initializeOneSignalPublic().catch(error => {
          console.warn('OneSignal initialization failed:', error);
        });
      }).catch(error => {
        console.warn('Failed to load OneSignal module:', error);
      });
    }

    // Initialize customer analytics (if enabled)
    if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID) {
      import('@/lib/analytics-public').then(({ initializeAnalytics }) => {
        initializeAnalytics().catch(error => {
          console.warn('Analytics initialization failed:', error);
        });
      }).catch(error => {
        console.warn('Failed to load analytics module:', error);
      });
    }

    // Block any admin-related functionality
    if (router?.pathname?.startsWith('/admin')) {
      console.error('Admin access blocked in public subdomain');
      router.replace('/404');
    }

    // Security: Remove any admin-related items from localStorage
    if (typeof window !== 'undefined') {
      const adminKeys = Object.keys(localStorage).filter(key => 
        key.includes('admin') || key.includes('staff') || key.includes('artist')
      );
      adminKeys.forEach(key => localStorage.removeItem(key));
    }

  }, [router]);

  // Block admin routes at the app level
  if (router?.pathname?.startsWith('/admin') || 
      router?.pathname?.startsWith('/staff') || 
      router?.pathname?.startsWith('/artist') ||
      router?.pathname?.startsWith('/apply')) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        fontFamily: 'Arial, sans-serif'
      }}>
        <h1>404 - Page Not Found</h1>
        <p>The page you're looking for doesn't exist.</p>
        <a href="/" style={{ color: '#3788d8', textDecoration: 'none' }}>
          Return to Home
        </a>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <CustomerProvider>
        <PWAProvider>
          <MemoizedHead />
          
          {/* The main application component */}
          <Component {...pageProps} />
          
          <MemoizedToastContainer />
        </PWAProvider>
      </CustomerProvider>
    </ErrorBoundary>
  );
}

export default MyApp
