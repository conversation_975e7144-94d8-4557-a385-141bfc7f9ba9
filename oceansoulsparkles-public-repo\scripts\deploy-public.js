#!/usr/bin/env node

/**
 * Ocean Soul Sparkles - Public Subdomain Deployment Script
 * 
 * This script handles the deployment of the public-facing website
 * with security validations and environment checks.
 */

const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local if it exists
const envPath = path.join(process.cwd(), '.env.local');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value && !process.env[key]) {
      process.env[key] = value;
    }
  });
}

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * Validate that no admin functionality exists
 */
function validateNoAdminFunctionality() {
  logInfo('Validating no admin functionality exists...');
  
  const blockedPaths = [
    'pages/admin',
    'components/admin',
    'styles/admin',
    'lib/admin-auth.js',
    'lib/admin-utils.js',
    'contexts/AuthContext.js' // Admin auth context
  ];
  
  let hasBlockedContent = false;
  
  blockedPaths.forEach(blockedPath => {
    const fullPath = path.join(process.cwd(), blockedPath);
    if (fs.existsSync(fullPath)) {
      logError(`Blocked admin content found: ${blockedPath}`);
      hasBlockedContent = true;
    }
  });
  
  if (hasBlockedContent) {
    logError('Admin functionality detected in public subdomain!');
    process.exit(1);
  }
  
  logSuccess('No admin functionality detected');
}

/**
 * Validate environment variables for public deployment
 */
function validateEnvironmentVariables() {
  logInfo('Validating environment variables...');
  
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_SITE_URL'
  ];
  
  const blockedEnvVars = [
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXT_PUBLIC_ADMIN_URL',
    'ENABLE_AUTH_BYPASS'
  ];
  
  // Check required variables
  requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
      logError(`Missing required environment variable: ${envVar}`);
      process.exit(1);
    }
  });
  
  // Check for blocked variables
  blockedEnvVars.forEach(envVar => {
    if (process.env[envVar] && process.env[envVar] !== 'false') {
      logError(`Blocked environment variable detected: ${envVar}=${process.env[envVar]}`);
      process.exit(1);
    }
  });
  
  // Validate admin access is disabled
  if (process.env.NEXT_PUBLIC_ADMIN_ACCESS === 'true') {
    logError('Admin access is enabled in public subdomain!');
    process.exit(1);
  }
  
  logSuccess('Environment variables validated');
}

/**
 * Validate package.json for public deployment
 */
function validatePackageJson() {
  logInfo('Validating package.json...');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    logError('package.json not found');
    process.exit(1);
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Validate project name
  if (packageJson.name !== 'oceansoulsparkles-public') {
    logError(`Invalid project name: ${packageJson.name}. Expected: oceansoulsparkles-public`);
    process.exit(1);
  }
  
  // Check for admin-related dependencies
  const blockedDependencies = [
    'admin-auth',
    'admin-utils',
    'staff-portal'
  ];
  
  const allDependencies = {
    ...packageJson.dependencies,
    ...packageJson.devDependencies
  };
  
  blockedDependencies.forEach(dep => {
    if (allDependencies[dep]) {
      logError(`Blocked dependency detected: ${dep}`);
      process.exit(1);
    }
  });
  
  logSuccess('package.json validated');
}

/**
 * Validate API routes for public access only
 */
function validateApiRoutes() {
  logInfo('Validating API routes...');
  
  const apiDir = path.join(process.cwd(), 'pages/api');
  
  if (!fs.existsSync(apiDir)) {
    logWarning('No API directory found');
    return;
  }
  
  const blockedApiPaths = [
    'pages/api/admin',
    'pages/api/staff',
    'pages/api/artist'
  ];
  
  blockedApiPaths.forEach(blockedPath => {
    const fullPath = path.join(process.cwd(), blockedPath);
    if (fs.existsSync(fullPath)) {
      logError(`Blocked API path found: ${blockedPath}`);
      process.exit(1);
    }
  });
  
  logSuccess('API routes validated');
}

/**
 * Run security audit
 */
function runSecurityAudit() {
  logInfo('Running security audit...');
  
  // Check for sensitive files
  const sensitiveFiles = [
    '.env.local',
    '.env.development',
    'admin-credentials.json',
    'service-account.json'
  ];
  
  sensitiveFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      logWarning(`Sensitive file detected: ${file} (ensure it's in .gitignore)`);
    }
  });
  
  // Validate .gitignore
  const gitignorePath = path.join(process.cwd(), '.gitignore');
  if (fs.existsSync(gitignorePath)) {
    const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
    const requiredIgnores = ['.env.local', '.env.development', '.env.production'];
    
    requiredIgnores.forEach(ignore => {
      if (!gitignoreContent.includes(ignore)) {
        logWarning(`Missing from .gitignore: ${ignore}`);
      }
    });
  }
  
  logSuccess('Security audit completed');
}

/**
 * Main deployment validation
 */
function main() {
  log('\n🚀 Ocean Soul Sparkles - Public Subdomain Deployment', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  try {
    validateRequiredFiles();
    validateNoAdminFunctionality();
    validateEnvironmentVariables();
    validatePackageJson();
    validateApiRoutes();
    runSecurityAudit();
    validateDeploymentReadiness();

    log('\n' + '=' .repeat(60), 'green');
    logSuccess('All validations passed! Ready for deployment.');
    log('=' .repeat(60), 'green');

    logInfo('Next steps:');
    logInfo('1. Run: npm install');
    logInfo('2. Run: npm run build');
    logInfo('3. Test locally: npm run start');
    logInfo('4. Deploy to Vercel: vercel --prod');
    logInfo('5. Configure domain: www.oceansoulsparkles.com.au');
    logInfo('6. Test public functionality');
    logInfo('7. Verify admin routes are blocked');
    logInfo('8. Monitor for security issues');

  } catch (error) {
    logError(`Deployment validation failed: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Validate that all required files exist
 */
function validateRequiredFiles() {
  logInfo('Validating required files exist...');

  const requiredFiles = [
    'package.json',
    'next.config.js',
    'middleware.js',
    'vercel.json',
    'pages/_app.js',
    'pages/index.js',
    'lib/supabase.js',
    'contexts/CustomerContext.js',
    'components/Layout.js',
    'components/ErrorBoundary.js',
    'styles/globals.css'
  ];

  let missingFiles = [];

  requiredFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file);
    }
  });

  if (missingFiles.length > 0) {
    logError(`Missing required files: ${missingFiles.join(', ')}`);
    process.exit(1);
  }

  logSuccess('All required files present');
}

/**
 * Validate deployment readiness
 */
function validateDeploymentReadiness() {
  logInfo('Validating deployment readiness...');

  // Check if build directory exists (should not exist before build)
  const buildDir = path.join(process.cwd(), '.next');
  if (fs.existsSync(buildDir)) {
    logWarning('Build directory exists. Run "npm run build" to ensure fresh build.');
  }

  // Check for development dependencies in production
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  if (process.env.NODE_ENV === 'production' && packageJson.devDependencies) {
    logWarning('Development dependencies detected. Ensure they are not included in production build.');
  }

  logSuccess('Deployment readiness validated');
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  validateNoAdminFunctionality,
  validateEnvironmentVariables,
  validatePackageJson,
  validateApiRoutes,
  runSecurityAudit,
  validateRequiredFiles,
  validateDeploymentReadiness
};
