import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import styles from '../../styles/admin/AdminHeader.module.css';

interface AdminHeaderProps {
  user: {
    id: string;
    email: string;
    role: 'DEV' | 'Admin' | 'Artist' | 'Braider';
    firstName: string;
    lastName: string;
  };
  onLogout: () => void;
  onToggleSidebar: () => void;
  sidebarCollapsed: boolean;
}

export default function AdminHeader({ user, onLogout, onToggleSidebar, sidebarCollapsed }: AdminHeaderProps) {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);
  const notificationsRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'DEV': return '#dc3545';
      case 'Admin': return '#3788d8';
      case 'Artist': return '#28a745';
      case 'Braider': return '#fd7e14';
      default: return '#6c757d';
    }
  };

  return (
    <header className={styles.adminHeader}>
      <div className={styles.headerLeft}>
        <button 
          className={styles.sidebarToggle}
          onClick={onToggleSidebar}
          title={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          <span className={styles.hamburger}>
            <span></span>
            <span></span>
            <span></span>
          </span>
        </button>

        <div className={styles.breadcrumb}>
          <Link href="/admin/dashboard" className={styles.breadcrumbLink}>
            Dashboard
          </Link>
          <span className={styles.breadcrumbSeparator}>/</span>
          <span className={styles.breadcrumbCurrent}>Overview</span>
        </div>
      </div>

      <div className={styles.headerRight}>
        {/* Quick Actions */}
        <div className={styles.quickActions}>
          <Link href="/admin/bookings/new" className={styles.quickAction} title="New Booking">
            📅
          </Link>
          <Link href="/admin/customers/new" className={styles.quickAction} title="New Customer">
            👤
          </Link>
          <button className={styles.quickAction} title="Refresh">
            🔄
          </button>
        </div>

        {/* Notifications */}
        <div className={styles.notifications} ref={notificationsRef}>
          <button 
            className={styles.notificationButton}
            onClick={() => setShowNotifications(!showNotifications)}
            title="Notifications"
          >
            🔔
            <span className={styles.notificationBadge}>3</span>
          </button>

          {showNotifications && (
            <div className={styles.notificationDropdown}>
              <div className={styles.notificationHeader}>
                <h3>Notifications</h3>
                <button className={styles.markAllRead}>Mark all read</button>
              </div>
              <div className={styles.notificationList}>
                <div className={styles.notificationItem}>
                  <div className={styles.notificationIcon}>📅</div>
                  <div className={styles.notificationContent}>
                    <div className={styles.notificationTitle}>New booking request</div>
                    <div className={styles.notificationTime}>5 minutes ago</div>
                  </div>
                </div>
                <div className={styles.notificationItem}>
                  <div className={styles.notificationIcon}>💰</div>
                  <div className={styles.notificationContent}>
                    <div className={styles.notificationTitle}>Payment received</div>
                    <div className={styles.notificationTime}>1 hour ago</div>
                  </div>
                </div>
                <div className={styles.notificationItem}>
                  <div className={styles.notificationIcon}>⚠️</div>
                  <div className={styles.notificationContent}>
                    <div className={styles.notificationTitle}>Low inventory alert</div>
                    <div className={styles.notificationTime}>2 hours ago</div>
                  </div>
                </div>
              </div>
              <div className={styles.notificationFooter}>
                <Link href="/admin/notifications">View all notifications</Link>
              </div>
            </div>
          )}
        </div>

        {/* User Menu */}
        <div className={styles.userMenu} ref={userMenuRef}>
          <button 
            className={styles.userButton}
            onClick={() => setShowUserMenu(!showUserMenu)}
          >
            <div className={styles.userAvatar}>
              {user.firstName.charAt(0)}{user.lastName.charAt(0)}
            </div>
            <div className={styles.userInfo}>
              <div className={styles.userName}>
                {user.firstName} {user.lastName}
              </div>
              <div 
                className={styles.userRole}
                style={{ color: getRoleColor(user.role) }}
              >
                {user.role}
              </div>
            </div>
            <div className={styles.dropdownArrow}>
              {showUserMenu ? '▲' : '▼'}
            </div>
          </button>

          {showUserMenu && (
            <div className={styles.userDropdown}>
              <div className={styles.userDropdownHeader}>
                <div className={styles.userEmail}>{user.email}</div>
                <div 
                  className={styles.userRoleBadge}
                  style={{ backgroundColor: getRoleColor(user.role) }}
                >
                  {user.role}
                </div>
              </div>
              
              <div className={styles.userDropdownMenu}>
                <Link href="/admin/profile" className={styles.dropdownItem}>
                  <span className={styles.dropdownIcon}>👤</span>
                  Profile Settings
                </Link>
                <Link href="/admin/security" className={styles.dropdownItem}>
                  <span className={styles.dropdownIcon}>🔒</span>
                  Security & MFA
                </Link>
                <Link href="/admin/preferences" className={styles.dropdownItem}>
                  <span className={styles.dropdownIcon}>⚙️</span>
                  Preferences
                </Link>
                <div className={styles.dropdownDivider}></div>
                <Link href="/admin/help" className={styles.dropdownItem}>
                  <span className={styles.dropdownIcon}>❓</span>
                  Help & Support
                </Link>
                <div className={styles.dropdownDivider}></div>
                <button 
                  className={`${styles.dropdownItem} ${styles.logoutItem}`}
                  onClick={onLogout}
                >
                  <span className={styles.dropdownIcon}>🚪</span>
                  Sign Out
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
