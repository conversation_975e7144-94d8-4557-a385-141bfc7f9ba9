/* Ocean Soul Sparkles - Customer Authentication Styles */

.authContainer {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.authContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/sparkle-pattern.png') repeat;
  opacity: 0.1;
  pointer-events: none;
}

.authCard {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 480px;
  position: relative;
  z-index: 1;
}

.authHeader {
  text-align: center;
  margin-bottom: 32px;
}

.authHeader h1 {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
}

.authHeader p {
  color: #6c757d;
  font-size: 16px;
  margin: 0;
}

.authForm {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
}

.formInput {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: white;
}

.formInput:focus {
  outline: none;
  border-color: #3788d8;
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.formInput:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.passwordInput {
  position: relative;
}

.passwordToggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.passwordToggle:hover {
  background-color: #f8f9fa;
}

.passwordToggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.checkboxGroup {
  margin: 8px 0;
}

.checkboxLabel {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
  line-height: 1.5;
}

.checkbox {
  margin-top: 2px;
  width: 16px;
  height: 16px;
  accent-color: #3788d8;
}

.checkboxText {
  color: #6c757d;
}

.authButton {
  background: linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%);
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.authButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 136, 216, 0.3);
}

.authButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.authButton.loading {
  pointer-events: none;
}

.buttonSpinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.authLinks {
  text-align: center;
  margin-top: 24px;
}

.authLinks p {
  color: #6c757d;
  font-size: 14px;
  margin: 8px 0;
}

.authLink {
  color: #3788d8;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
}

.authLink:hover {
  color: #2c6cb7;
  text-decoration: underline;
}

.guestOption {
  margin-top: 24px;
  text-align: center;
}

.divider {
  position: relative;
  margin: 20px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e9ecef;
}

.divider span {
  background: white;
  color: #6c757d;
  padding: 0 16px;
  font-size: 14px;
  position: relative;
}

.authFooter {
  margin-top: 24px;
  text-align: center;
  max-width: 480px;
}

.authFooter p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: 1.5;
}

.authFooter .authLink {
  color: rgba(255, 255, 255, 0.9);
}

.authFooter .authLink:hover {
  color: white;
}

.loadingSpinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #3788d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .authContainer {
    padding: 16px;
  }

  .authCard {
    padding: 24px;
    border-radius: 12px;
  }

  .authHeader h1 {
    font-size: 24px;
  }

  .formRow {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .authButton {
    padding: 16px 24px;
  }
}

@media (max-width: 480px) {
  .authCard {
    padding: 20px;
    margin: 0 8px;
  }

  .authHeader h1 {
    font-size: 22px;
  }

  .formInput {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}
