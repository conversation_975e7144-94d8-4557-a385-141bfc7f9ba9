import { supabase } from '@/lib/supabase';

/**
 * Public API endpoint for creating customer bookings
 * This endpoint allows both authenticated customers and guests to create booking requests
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const bookingData = req.body;

    // Validate required fields
    const requiredFields = [
      'service_id',
      'service_name',
      'customer_info',
      'booking_details'
    ];

    for (const field of requiredFields) {
      if (!bookingData[field]) {
        return res.status(400).json({ error: `Missing required field: ${field}` });
      }
    }

    // Validate customer info
    const { customer_info } = bookingData;
    const requiredCustomerFields = ['first_name', 'last_name', 'email', 'phone'];
    
    for (const field of requiredCustomerFields) {
      if (!customer_info[field]) {
        return res.status(400).json({ error: `Missing customer field: ${field}` });
      }
    }

    // Validate booking details
    const { booking_details } = bookingData;
    const requiredBookingFields = ['preferred_date', 'preferred_time', 'event_type', 'location'];
    
    for (const field of requiredBookingFields) {
      if (!booking_details[field]) {
        return res.status(400).json({ error: `Missing booking field: ${field}` });
      }
    }

    // Validate date is in the future
    const preferredDate = new Date(booking_details.preferred_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (preferredDate < today) {
      return res.status(400).json({ error: 'Booking date must be in the future' });
    }

    // Prepare booking record for database
    const bookingRecord = {
      // Service information
      service_id: bookingData.service_id,
      service_name: bookingData.service_name,
      
      // Customer information
      customer_first_name: customer_info.first_name,
      customer_last_name: customer_info.last_name,
      customer_email: customer_info.email,
      customer_phone: customer_info.phone,
      customer_id: customer_info.customer_id || null,
      is_guest_booking: !customer_info.is_authenticated,
      
      // Booking details
      preferred_date: booking_details.preferred_date,
      preferred_time: booking_details.preferred_time,
      alternative_date: booking_details.alternative_date || null,
      alternative_time: booking_details.alternative_time || null,
      event_type: booking_details.event_type,
      number_of_people: booking_details.number_of_people || 1,
      location_type: booking_details.location,
      location_address: booking_details.address || null,
      special_requests: booking_details.special_requests || null,
      
      // Pricing
      base_price: bookingData.pricing?.base_price || 0,
      estimated_total: bookingData.pricing?.estimated_total || 0,
      
      // Status and metadata
      status: 'pending',
      booking_source: 'public_website',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('[Booking API] Creating booking request:', {
      service: bookingRecord.service_name,
      customer: `${bookingRecord.customer_first_name} ${bookingRecord.customer_last_name}`,
      date: bookingRecord.preferred_date,
      is_guest: bookingRecord.is_guest_booking
    });

    // Insert booking into database
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .insert([bookingRecord])
      .select()
      .single();

    if (bookingError) {
      console.error('[Booking API] Database error:', bookingError);
      
      // Handle specific database errors
      if (bookingError.code === '23505') {
        return res.status(409).json({ 
          error: 'A booking with these details already exists' 
        });
      }
      
      return res.status(500).json({ 
        error: 'Failed to create booking request',
        details: process.env.NODE_ENV === 'development' ? bookingError.message : undefined
      });
    }

    // Send confirmation email (optional - implement if email service is available)
    try {
      await sendBookingConfirmationEmail(booking);
    } catch (emailError) {
      console.warn('[Booking API] Email notification failed:', emailError);
      // Don't fail the booking if email fails
    }

    // Send notification to admin (optional - implement if notification service is available)
    try {
      await notifyAdminOfNewBooking(booking);
    } catch (notificationError) {
      console.warn('[Booking API] Admin notification failed:', notificationError);
      // Don't fail the booking if notification fails
    }

    console.log('[Booking API] Booking created successfully:', booking.id);

    // Return success response
    return res.status(201).json({
      success: true,
      booking: {
        id: booking.id,
        status: booking.status,
        service_name: booking.service_name,
        preferred_date: booking.preferred_date,
        preferred_time: booking.preferred_time,
        estimated_total: booking.estimated_total
      },
      message: 'Booking request submitted successfully! We will contact you to confirm your appointment.'
    });

  } catch (error) {
    console.error('[Booking API] Unexpected error:', error);
    
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred while processing your booking request'
    });
  }
}

/**
 * Send booking confirmation email to customer
 */
async function sendBookingConfirmationEmail(booking) {
  // Placeholder for email service integration
  // In a real implementation, you would integrate with your email service
  console.log('[Email] Sending confirmation email to:', booking.customer_email);
  
  // Example email content
  const emailContent = {
    to: booking.customer_email,
    subject: 'Booking Request Received - Ocean Soul Sparkles',
    template: 'booking-confirmation',
    data: {
      customer_name: `${booking.customer_first_name} ${booking.customer_last_name}`,
      service_name: booking.service_name,
      preferred_date: booking.preferred_date,
      preferred_time: booking.preferred_time,
      booking_id: booking.id
    }
  };
  
  // TODO: Implement actual email sending
  // await emailService.send(emailContent);
}

/**
 * Notify admin of new booking
 */
async function notifyAdminOfNewBooking(booking) {
  // Placeholder for admin notification
  // In a real implementation, you would send notifications via email, SMS, or push notifications
  console.log('[Notification] New booking received:', {
    id: booking.id,
    service: booking.service_name,
    customer: `${booking.customer_first_name} ${booking.customer_last_name}`,
    date: booking.preferred_date
  });
  
  // TODO: Implement actual admin notification
  // await notificationService.notifyAdmin('new_booking', booking);
}
