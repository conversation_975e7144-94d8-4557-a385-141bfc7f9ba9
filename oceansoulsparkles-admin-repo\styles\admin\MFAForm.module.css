/* MFA Form Component Styles */
.mfaContainer {
  background: var(--admin-bg-primary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-xl);
  box-shadow: 0 8px 24px var(--admin-shadow-medium);
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: var(--admin-spacing-xl);
}

.header h2 {
  color: var(--admin-darker);
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 var(--admin-spacing-sm) 0;
}

.header p {
  color: var(--admin-gray);
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.errorAlert {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: var(--admin-spacing-md);
  border-radius: var(--admin-radius-md);
  margin-bottom: var(--admin-spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
}

.errorIcon {
  font-size: 1.1rem;
  flex-shrink: 0;
}

.errorMessage {
  flex: 1;
  font-size: 0.9rem;
  font-weight: 500;
}

.form {
  display: flex;
  flex-direction: column;
  gap: var(--admin-spacing-lg);
}

.inputSection {
  text-align: center;
}

.inputLabel {
  display: block;
  color: var(--admin-darker);
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: var(--admin-spacing-md);
}

.codeInputContainer {
  display: flex;
  justify-content: center;
  gap: var(--admin-spacing-sm);
  margin-bottom: var(--admin-spacing-md);
}

.codeInput {
  width: 50px;
  height: 60px;
  border: 2px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--admin-darker);
  background: var(--admin-bg-primary);
  transition: all var(--admin-transition-normal);
}

.codeInput:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
  transform: scale(1.05);
}

.codeInput.filled {
  border-color: var(--admin-success);
  background: rgba(40, 167, 69, 0.05);
}

.codeInput.error {
  border-color: var(--admin-danger);
  background: rgba(220, 53, 69, 0.05);
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.inputHint {
  color: var(--admin-gray);
  font-size: 0.85rem;
  margin-bottom: var(--admin-spacing-lg);
}

.submitButton {
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-dark));
  color: white;
  border: none;
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  border-radius: var(--admin-radius-md);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-spacing-sm);
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 136, 216, 0.3);
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loadingContainer {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.buttonIcon {
  font-size: 1.2rem;
  transition: transform var(--admin-transition-normal);
}

.submitButton:hover:not(:disabled) .buttonIcon {
  transform: translateX(2px);
}

.footer {
  margin-top: var(--admin-spacing-xl);
  padding-top: var(--admin-spacing-lg);
  border-top: 1px solid var(--admin-border-light);
  text-align: center;
}

.backButton {
  color: var(--admin-gray);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color var(--admin-transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--admin-spacing-xs);
}

.backButton:hover {
  color: var(--admin-primary);
}

.backIcon {
  font-size: 1rem;
  transition: transform var(--admin-transition-normal);
}

.backButton:hover .backIcon {
  transform: translateX(-2px);
}

.helpText {
  margin-top: var(--admin-spacing-md);
  color: var(--admin-gray);
  font-size: 0.8rem;
  line-height: 1.4;
}

.helpText a {
  color: var(--admin-primary);
  text-decoration: none;
  font-weight: 500;
}

.helpText a:hover {
  text-decoration: underline;
}

/* Security Indicators */
.securityBadge {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-spacing-xs);
  background: rgba(40, 167, 69, 0.1);
  color: var(--admin-success);
  padding: var(--admin-spacing-xs) var(--admin-spacing-sm);
  border-radius: var(--admin-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: var(--admin-spacing-sm);
}

.securityIcon {
  font-size: 0.9rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .mfaContainer {
    padding: var(--admin-spacing-lg);
    margin: var(--admin-spacing-md);
  }

  .header h2 {
    font-size: 1.6rem;
  }

  .codeInputContainer {
    gap: var(--admin-spacing-xs);
  }

  .codeInput {
    width: 45px;
    height: 55px;
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .mfaContainer {
    padding: var(--admin-spacing-md);
  }

  .header h2 {
    font-size: 1.4rem;
  }

  .codeInput {
    width: 40px;
    height: 50px;
    font-size: 1.2rem;
  }

  .submitButton {
    padding: var(--admin-spacing-sm) var(--admin-spacing-md);
    font-size: 0.9rem;
  }
}
