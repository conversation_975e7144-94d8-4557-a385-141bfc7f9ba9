"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "staticGenerationAsyncStorage", {
    enumerable: true,
    get: function() {
        return staticGenerationAsyncStorage;
    }
});
const _asynclocalstorage = require("./async-local-storage");
const staticGenerationAsyncStorage = (0, _asynclocalstorage.createAsyncLocalStorage)();

if ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {
  Object.defineProperty(exports.default, '__esModule', { value: true });
  Object.assign(exports.default, exports);
  module.exports = exports.default;
}

//# sourceMappingURL=static-generation-async-storage-instance.js.map