import { useState, useEffect } from 'react';
import Head from 'next/head';
import Layout from '@/components/Layout';
import ProductCard from '@/components/ProductCard';
import { publicData } from '@/lib/supabase';
import { toast } from 'react-toastify';
import styles from '@/styles/Shop.module.css';

export default function Shop() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch products on component mount
  useEffect(() => {
    fetchProducts();
  }, [selectedCategory]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const { data, error } = await publicData.getProducts(selectedCategory);
      
      if (error) {
        console.error('Error fetching products:', error);
        toast.error('Unable to load products. Please try again.');
        return;
      }

      setProducts(data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Unable to load products. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Get unique categories from products
  const categories = ['all', ...new Set(products.map(product => product.category_name))];

  // Filter products by search term
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Layout>
      <Head>
        <title>Shop - Ocean Soul Sparkles</title>
        <meta name="description" content="Shop eco-friendly glitter, face paints, and beauty products from Ocean Soul Sparkles. Biodegradable glitter made from eucalyptus trees." />
        <meta name="keywords" content="eco-friendly glitter, biodegradable glitter, face paint, beauty products, sustainable makeup, Melbourne" />
      </Head>

      <div className={styles.shopContainer}>
        <div className={styles.shopHeader}>
          <h1>Eco-Friendly Beauty Shop</h1>
          <p>Discover our range of sustainable, biodegradable beauty products</p>
        </div>

        {/* Search and Filter */}
        <div className={styles.shopControls}>
          <div className={styles.searchContainer}>
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
            <svg className={styles.searchIcon} xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </div>

          {categories.length > 1 && (
            <div className={styles.categoryFilter}>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className={styles.categorySelect}
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>

        {/* Products Grid */}
        <div className={styles.productsSection}>
          {loading ? (
            <div className={styles.loadingContainer}>
              <div className={styles.loadingSpinner}></div>
              <p>Loading products...</p>
            </div>
          ) : filteredProducts.length > 0 ? (
            <div className={styles.productsGrid}>
              {filteredProducts.map(product => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className={styles.noProducts}>
              <div className={styles.noProductsIcon}>🛍️</div>
              <h3>No products found</h3>
              <p>
                {searchTerm 
                  ? `No products match "${searchTerm}"`
                  : selectedCategory === 'all' 
                    ? 'No products are currently available.' 
                    : `No products found in the "${selectedCategory}" category.`
                }
              </p>
              {(searchTerm || selectedCategory !== 'all') && (
                <button 
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('all');
                  }}
                  className={styles.clearFiltersButton}
                >
                  Clear Filters
                </button>
              )}
            </div>
          )}
        </div>

        {/* Eco-Friendly Information */}
        <div className={styles.ecoInfo}>
          <div className={styles.ecoInfoGrid}>
            <div className={styles.ecoInfoCard}>
              <div className={styles.ecoIcon}>🌿</div>
              <h3>100% Biodegradable</h3>
              <p>Our glitter is made from eucalyptus trees and breaks down naturally in just 6 weeks in soil.</p>
            </div>
            <div className={styles.ecoInfoCard}>
              <div className={styles.ecoIcon}>🐰</div>
              <h3>Cruelty-Free & Vegan</h3>
              <p>All our products are never tested on animals and contain no animal-derived ingredients.</p>
            </div>
            <div className={styles.ecoInfoCard}>
              <div className={styles.ecoIcon}>🌊</div>
              <h3>Ocean-Safe</h3>
              <p>Our biodegradable products won't harm marine life or pollute waterways.</p>
            </div>
            <div className={styles.ecoInfoCard}>
              <div className={styles.ecoIcon}>✨</div>
              <h3>Professional Quality</h3>
              <p>Used by professional artists worldwide for stunning, long-lasting results.</p>
            </div>
          </div>
        </div>

        {/* Shipping Information */}
        <div className={styles.shippingInfo}>
          <h3>Shipping & Returns</h3>
          <div className={styles.shippingGrid}>
            <div className={styles.shippingCard}>
              <h4>🚚 Fast Shipping</h4>
              <p>Free shipping on orders over $50. Express delivery available.</p>
            </div>
            <div className={styles.shippingCard}>
              <h4>↩️ Easy Returns</h4>
              <p>30-day return policy on unopened products. Customer satisfaction guaranteed.</p>
            </div>
            <div className={styles.shippingCard}>
              <h4>💳 Secure Payments</h4>
              <p>All payments processed securely through Square. Major credit cards accepted.</p>
            </div>
          </div>
        </div>

        {/* Contact for Custom Orders */}
        <div className={styles.customOrders}>
          <h3>Need Something Special?</h3>
          <p>Looking for custom colors or bulk orders? We'd love to help create something unique for you!</p>
          <div className={styles.contactActions}>
            <a href="/contact" className="button">Contact Us</a>
            <a href="mailto:<EMAIL>" className="button button--outline">
              Email Us
            </a>
          </div>
        </div>
      </div>
    </Layout>
  );
}
