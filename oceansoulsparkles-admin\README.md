# Ocean Soul Sparkles - Admin Subdomain

🔒 **Secure Admin Portal for Ocean Soul Sparkles Business Management**

This is the administrative subdomain for Ocean Soul Sparkles, providing secure access to business management tools, staff portals, and system administration features.

## 🌟 Features

### Core Admin Features
- **Secure Authentication** - Multi-factor authentication with role-based access control
- **Dashboard Overview** - Real-time business metrics and activity monitoring
- **Booking Management** - Complete booking lifecycle management
- **Customer Management** - Customer database and relationship management
- **Staff Portal** - Artist and braider management with performance tracking
- **Service Management** - Service catalog and pricing management
- **Product Management** - Inventory and product catalog management
- **Financial Reports** - Revenue tracking and financial analytics
- **System Administration** - Configuration and security management

### Security Features
- **5-Tier Role System** - DEV, Admin, Artist, Braider, User roles
- **Multi-Factor Authentication** - TOTP-based 2FA for enhanced security
- **Rate Limiting** - Protection against brute force attacks
- **Audit Logging** - Comprehensive activity tracking and monitoring
- **IP Restrictions** - Optional IP whitelisting for admin access
- **Session Management** - Secure session handling with automatic timeouts
- **Encryption** - End-to-end encryption for sensitive data

### Technical Features
- **Next.js 14** - Modern React framework with App Router
- **TypeScript** - Full type safety and developer experience
- **Supabase Integration** - Real-time database with Row Level Security
- **Square Payments** - Integrated payment processing
- **Responsive Design** - Mobile-first admin interface
- **Real-time Updates** - Live data synchronization
- **Progressive Web App** - Offline capabilities and app-like experience

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Supabase account and project
- Square developer account (for payments)

### Installation

1. **Clone and Setup**
   ```bash
   cd oceansoulsparkles-admin
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Required Environment Variables**
   ```env
   # Admin Subdomain Configuration
   NEXT_PUBLIC_ADMIN_SUBDOMAIN=true
   NEXT_PUBLIC_SITE_URL=https://admin.oceansoulsparkles.com.au
   NEXT_PUBLIC_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au

   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

   # Authentication & Security
   NEXTAUTH_URL=https://admin.oceansoulsparkles.com.au
   NEXTAUTH_SECRET=your_nextauth_secret_32_chars_minimum
   JWT_SECRET=your_jwt_secret_for_admin_tokens_32_chars
   ENCRYPTION_KEY=your_32_character_encryption_key_here

   # Square Payment Integration
   NEXT_PUBLIC_SQUARE_APPLICATION_ID=your_square_app_id
   NEXT_PUBLIC_SQUARE_LOCATION_ID=your_square_location_id
   SQUARE_ACCESS_TOKEN=your_square_access_token
   ```

4. **Database Setup**
   - Ensure Supabase database is configured with proper RLS policies
   - Run database migrations if needed
   - Set up user roles and permissions

5. **Build and Deploy**
   ```bash
   # Development
   npm run dev

   # Production Build
   npm run build
   npm start

   # Environment Validation
   npm run check-env

   # Deployment Validation
   npm run deploy-check
   ```

## 🏗️ Project Structure

```
oceansoulsparkles-admin/
├── components/
│   ├── admin/           # Admin-specific components
│   ├── auth/            # Authentication components
│   └── common/          # Shared components
├── lib/
│   ├── auth/            # Authentication logic
│   ├── security/        # Security utilities
│   └── utils/           # Helper functions
├── pages/
│   ├── admin/           # Admin pages
│   ├── api/             # API routes
│   └── auth/            # Authentication pages
├── styles/
│   ├── admin/           # Admin-specific styles
│   └── globals.css      # Global styles
├── scripts/
│   ├── check-env.js     # Environment validation
│   └── deploy-admin.js  # Deployment validation
└── middleware.ts        # Route protection
```

## 🔐 Security Architecture

### Authentication Flow
1. **Login** - Email/password with rate limiting
2. **MFA Verification** - TOTP-based two-factor authentication
3. **Session Management** - Secure JWT tokens with automatic refresh
4. **Role Verification** - Continuous role-based access control
5. **Audit Logging** - All actions logged for security monitoring

### Role-Based Access Control
- **DEV** - Full system access and development tools
- **Admin** - Complete business management access
- **Artist** - Service delivery and customer interaction
- **Braider** - Specialized braiding services and scheduling
- **User** - Basic authenticated access (future use)

### Security Measures
- **Rate Limiting** - 5 attempts per 15 minutes per IP
- **Session Timeout** - 8 hours for admin, 4 hours for staff
- **IP Restrictions** - Optional whitelist for admin access
- **Encryption** - AES-256 encryption for sensitive data
- **HTTPS Only** - Secure transport layer
- **CSP Headers** - Content Security Policy protection

## 📊 Admin Dashboard

### Overview Metrics
- **Revenue Tracking** - Real-time financial performance
- **Booking Analytics** - Service demand and scheduling insights
- **Customer Metrics** - Growth and retention analytics
- **Staff Performance** - Individual and team productivity

### Quick Actions
- **New Booking** - Rapid booking creation
- **Customer Management** - Add/edit customer information
- **Service Configuration** - Update service offerings
- **Staff Scheduling** - Manage artist availability

### Real-time Features
- **Live Notifications** - Instant updates on bookings and payments
- **Activity Feed** - Recent system activity and user actions
- **Status Monitoring** - System health and performance metrics

## 🎨 Staff Portal Features

### Artist/Braider Dashboard
- **Personal Schedule** - Individual booking calendar
- **Performance Metrics** - Earnings and rating tracking
- **Customer History** - Service history and preferences
- **Availability Management** - Set working hours and time off

### Booking Management
- **Service Assignment** - Automatic and manual booking assignment
- **Customer Communication** - Integrated messaging system
- **Service Documentation** - Photo uploads and service notes
- **Payment Processing** - Secure payment handling

## 🔧 Development

### Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
npm run test         # Run test suite
npm run check-env    # Validate environment variables
npm run deploy-check # Pre-deployment validation
```

### Environment Validation
The build process includes comprehensive environment validation:
- **Required Variables** - Ensures all critical settings are configured
- **Security Checks** - Validates encryption keys and secrets
- **Development Flags** - Prevents debug mode in production
- **API Configuration** - Verifies external service connections

## 🚀 Deployment

### Vercel Deployment
1. **Environment Setup**
   - Configure all environment variables in Vercel dashboard
   - Set up custom domain: `admin.oceansoulsparkles.com.au`
   - Configure DNS records

2. **Security Configuration**
   - Enable HTTPS redirect
   - Configure security headers
   - Set up monitoring and alerts

3. **Database Configuration**
   - Ensure Supabase RLS policies are active
   - Configure connection pooling
   - Set up backup procedures

### Production Checklist
- [ ] All environment variables configured
- [ ] Security headers enabled
- [ ] SSL certificate active
- [ ] Database RLS policies enabled
- [ ] Audit logging functional
- [ ] Rate limiting configured
- [ ] MFA enabled for admin accounts
- [ ] Backup procedures in place
- [ ] Monitoring and alerts configured

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User authentication
- `POST /api/auth/mfa-verify` - MFA verification
- `GET /api/auth/verify` - Token validation
- `POST /api/auth/logout` - Session termination

### Admin Endpoints
- `GET /api/admin/dashboard` - Dashboard data
- `GET /api/admin/users` - User management
- `GET /api/admin/bookings` - Booking management
- `GET /api/admin/analytics` - Business analytics

## 🔍 Monitoring & Maintenance

### Health Checks
- **Database Connectivity** - Supabase connection monitoring
- **API Performance** - Response time tracking
- **Authentication System** - Login success rates
- **Security Events** - Failed login attempts and suspicious activity

### Maintenance Tasks
- **Log Rotation** - Automated audit log management
- **Session Cleanup** - Expired session removal
- **Performance Optimization** - Database query optimization
- **Security Updates** - Regular dependency updates

## 🆘 Support & Troubleshooting

### Common Issues
1. **Build Failures** - Check environment variables with `npm run check-env`
2. **Authentication Issues** - Verify JWT secrets and Supabase configuration
3. **Database Errors** - Check RLS policies and connection settings
4. **Performance Issues** - Monitor API response times and database queries

### Getting Help
- Check the deployment validation script: `npm run deploy-check`
- Review audit logs for security events
- Monitor system health dashboard
- Contact development team for critical issues

## 📄 License

This project is proprietary software for Ocean Soul Sparkles business operations.

---

**🌊 Ocean Soul Sparkles Admin Portal - Secure, Scalable, Beautiful** ✨
