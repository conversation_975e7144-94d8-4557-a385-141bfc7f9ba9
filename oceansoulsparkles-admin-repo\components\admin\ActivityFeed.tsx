import styles from '../../styles/admin/ActivityFeed.module.css';

interface Activity {
  id: string;
  type: 'booking' | 'payment' | 'customer' | 'system' | 'user';
  title: string;
  description: string;
  timestamp: string;
  user?: string;
  metadata?: Record<string, any>;
}

interface ActivityFeedProps {
  activities?: Activity[];
  userRole: 'DEV' | 'Admin' | 'Artist' | 'Braider';
}

export default function ActivityFeed({ activities, userRole }: ActivityFeedProps) {
  const sampleActivities: Activity[] = activities || [
    {
      id: '1',
      type: 'booking',
      title: 'New booking created',
      description: '<PERSON> booked Festival Face Paint for Jan 15',
      timestamp: '2024-01-14T10:30:00Z',
      user: '<PERSON>',
      metadata: { bookingId: 'BK001', amount: 85 }
    },
    {
      id: '2',
      type: 'payment',
      title: 'Payment received',
      description: 'Payment of $120 received for booking BK002',
      timestamp: '2024-01-14T09:45:00Z',
      user: 'System',
      metadata: { amount: 120, method: 'card' }
    },
    {
      id: '3',
      type: 'customer',
      title: 'New customer registered',
      description: '<PERSON> created a new account',
      timestamp: '2024-01-14T09:15:00Z',
      user: '<PERSON>',
      metadata: { customerId: 'CUST123' }
    },
    {
      id: '4',
      type: 'booking',
      title: 'Booking confirmed',
      description: 'Hair Braiding appointment confirmed for Jan 15',
      timestamp: '2024-01-14T08:30:00Z',
      user: 'Lisa Brown',
      metadata: { bookingId: 'BK002' }
    },
    {
      id: '5',
      type: 'system',
      title: 'Inventory alert',
      description: 'Low stock alert for Glitter - Gold',
      timestamp: '2024-01-14T08:00:00Z',
      user: 'System',
      metadata: { productId: 'PROD456', stock: 5 }
    },
    {
      id: '6',
      type: 'user',
      title: 'Staff login',
      description: 'Sophie Taylor logged into admin portal',
      timestamp: '2024-01-14T07:45:00Z',
      user: 'Sophie Taylor',
      metadata: { role: 'Artist' }
    },
    {
      id: '7',
      type: 'booking',
      title: 'Booking completed',
      description: 'Glitter Art Design session completed',
      timestamp: '2024-01-13T16:00:00Z',
      user: 'Sophie Taylor',
      metadata: { bookingId: 'BK003', rating: 5 }
    },
    {
      id: '8',
      type: 'payment',
      title: 'Refund processed',
      description: 'Refund of $200 processed for cancelled booking',
      timestamp: '2024-01-13T14:30:00Z',
      user: 'Admin',
      metadata: { amount: 200, bookingId: 'BK004' }
    }
  ];

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return time.toLocaleDateString('en-AU', { month: 'short', day: 'numeric' });
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'booking': return '📅';
      case 'payment': return '💰';
      case 'customer': return '👤';
      case 'system': return '⚙️';
      case 'user': return '👨‍💼';
      default: return '📝';
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'booking': return '#3788d8';
      case 'payment': return '#28a745';
      case 'customer': return '#4ECDC4';
      case 'system': return '#ffc107';
      case 'user': return '#6f42c1';
      default: return '#6c757d';
    }
  };

  const shouldShowActivity = (activity: Activity) => {
    // Filter activities based on user role
    if (userRole === 'Artist' || userRole === 'Braider') {
      // Show only relevant activities for artists/braiders
      return activity.type === 'booking' || 
             (activity.type === 'payment' && activity.user === 'Current User') ||
             activity.type === 'customer';
    }
    return true; // Admins and DEV see all activities
  };

  const filteredActivities = sampleActivities.filter(shouldShowActivity);

  return (
    <div className={styles.activityFeedContainer}>
      <div className={styles.header}>
        <h2 className={styles.sectionTitle}>Recent Activity</h2>
        <p className={styles.sectionSubtitle}>Latest system events</p>
      </div>

      <div className={styles.activityList}>
        {filteredActivities.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>📝</div>
            <h3>No recent activity</h3>
            <p>Activity will appear here as events occur</p>
          </div>
        ) : (
          filteredActivities.map((activity) => (
            <div key={activity.id} className={styles.activityItem}>
              <div className={styles.activityIconContainer}>
                <div 
                  className={styles.activityIcon}
                  style={{ backgroundColor: getActivityColor(activity.type) }}
                >
                  {getActivityIcon(activity.type)}
                </div>
                <div className={styles.activityLine}></div>
              </div>

              <div className={styles.activityContent}>
                <div className={styles.activityHeader}>
                  <div className={styles.activityTitle}>{activity.title}</div>
                  <div className={styles.activityTime}>
                    {formatTimeAgo(activity.timestamp)}
                  </div>
                </div>

                <div className={styles.activityDescription}>
                  {activity.description}
                </div>

                {activity.user && (
                  <div className={styles.activityUser}>
                    <span className={styles.userIcon}>👤</span>
                    <span className={styles.userName}>{activity.user}</span>
                  </div>
                )}

                {activity.metadata && (
                  <div className={styles.activityMetadata}>
                    {activity.metadata.amount && (
                      <span className={styles.metadataItem}>
                        💰 ${activity.metadata.amount}
                      </span>
                    )}
                    {activity.metadata.bookingId && (
                      <span className={styles.metadataItem}>
                        📋 {activity.metadata.bookingId}
                      </span>
                    )}
                    {activity.metadata.rating && (
                      <span className={styles.metadataItem}>
                        ⭐ {activity.metadata.rating}/5
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Activity Summary */}
      <div className={styles.activitySummary}>
        <div className={styles.summaryItem}>
          <div className={styles.summaryIcon}>📅</div>
          <div className={styles.summaryText}>
            <div className={styles.summaryValue}>
              {filteredActivities.filter(a => a.type === 'booking').length}
            </div>
            <div className={styles.summaryLabel}>Bookings</div>
          </div>
        </div>

        <div className={styles.summaryItem}>
          <div className={styles.summaryIcon}>💰</div>
          <div className={styles.summaryText}>
            <div className={styles.summaryValue}>
              {filteredActivities.filter(a => a.type === 'payment').length}
            </div>
            <div className={styles.summaryLabel}>Payments</div>
          </div>
        </div>

        <div className={styles.summaryItem}>
          <div className={styles.summaryIcon}>👤</div>
          <div className={styles.summaryText}>
            <div className={styles.summaryValue}>
              {filteredActivities.filter(a => a.type === 'customer').length}
            </div>
            <div className={styles.summaryLabel}>Customers</div>
          </div>
        </div>
      </div>
    </div>
  );
}
