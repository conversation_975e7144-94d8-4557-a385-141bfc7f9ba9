import { useState } from 'react';
import styles from '@/styles/ServiceSelector.module.css';

export default function ServiceSelector({ services, onServiceSelect, loading }) {
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Get unique categories from services
  const categories = ['all', ...new Set(services.map(service => service.category))];

  // Filter services by category
  const filteredServices = selectedCategory === 'all' 
    ? services 
    : services.filter(service => service.category === selectedCategory);

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0 && mins > 0) {
      return `${hours}h ${mins}m`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${mins}m`;
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading services...</p>
      </div>
    );
  }

  return (
    <div className={styles.serviceSelectorContainer}>
      {/* Category Filter */}
      {categories.length > 1 && (
        <div className={styles.categoryFilter}>
          <h3>Filter by Category</h3>
          <div className={styles.categoryButtons}>
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`${styles.categoryButton} ${
                  selectedCategory === category ? styles.active : ''
                }`}
              >
                {category === 'all' ? 'All Services' : category}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Services Grid */}
      <div className={styles.servicesGrid}>
        {filteredServices.map(service => (
          <div key={service.id} className={styles.serviceCard}>
            {service.image_url && (
              <div className={styles.serviceImage}>
                <img 
                  src={service.image_url} 
                  alt={service.name}
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              </div>
            )}
            
            <div className={styles.serviceContent}>
              <div className={styles.serviceHeader}>
                <h3 className={styles.serviceName}>{service.name}</h3>
                <span className={styles.serviceCategory}>{service.category}</span>
              </div>

              <p className={styles.serviceDescription}>{service.description}</p>

              <div className={styles.serviceDetails}>
                <div className={styles.serviceDetail}>
                  <span className={styles.detailIcon}>⏱️</span>
                  <span>{formatDuration(service.duration)}</span>
                </div>
                <div className={styles.serviceDetail}>
                  <span className={styles.detailIcon}>💰</span>
                  <span>From ${service.price}</span>
                </div>
              </div>

              {service.features && service.features.length > 0 && (
                <div className={styles.serviceFeatures}>
                  <h4>Includes:</h4>
                  <ul>
                    {service.features.slice(0, 3).map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                    {service.features.length > 3 && (
                      <li>+ {service.features.length - 3} more...</li>
                    )}
                  </ul>
                </div>
              )}

              {service.suitable_for && (
                <div className={styles.suitableFor}>
                  <span className={styles.suitableLabel}>Perfect for:</span>
                  <span className={styles.suitableText}>{service.suitable_for}</span>
                </div>
              )}

              <button
                onClick={() => onServiceSelect(service)}
                className={styles.selectButton}
              >
                Select This Service
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredServices.length === 0 && !loading && (
        <div className={styles.noServices}>
          <div className={styles.noServicesIcon}>🎨</div>
          <h3>No services found</h3>
          <p>
            {selectedCategory === 'all' 
              ? 'No services are currently available.' 
              : `No services found in the "${selectedCategory}" category.`
            }
          </p>
          {selectedCategory !== 'all' && (
            <button 
              onClick={() => setSelectedCategory('all')}
              className={styles.showAllButton}
            >
              Show All Services
            </button>
          )}
        </div>
      )}
    </div>
  );
}
