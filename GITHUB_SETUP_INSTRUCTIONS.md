# GitHub Repository Setup & Deployment Instructions

## 🎯 Overview

This guide will help you create and deploy both Ocean Soul Sparkles repositories:
- **Admin Portal**: `oceansoulsparkles-admin` (Private)
- **Public Website**: `oceansoulsparkles-public` (Public/Private)

## 📋 Prerequisites

- GitHub account
- Git installed locally
- Vercel account
- Access to Squarespace DNS settings

## 🔧 Step 1: Create GitHub Repositories

### 1.1 Create Admin Repository

1. **Go to GitHub.com** and sign in
2. Click the **"+"** button → **"New repository"**
3. **Repository settings**:
   - Repository name: `oceansoulsparkles-admin`
   - Description: `Ocean Soul Sparkles Admin Portal - Staff Management System`
   - Visibility: **Private** (for security)
   - Don't initialize with README (we have existing code)
4. Click **"Create repository"**

### 1.2 Create Public Repository

1. Click the **"+"** button → **"New repository"**
2. **Repository settings**:
   - Repository name: `oceansoulsparkles-public`
   - Description: `Ocean Soul Sparkles Public Website - Customer Booking Portal`
   - Visibility: **Public** or **Private** (your choice)
   - Don't initialize with README (we have existing code)
3. Click **"Create repository"**

## 🚀 Step 2: Push Repositories to GitHub

### 2.1 Push Admin Repository

```bash
# Navigate to admin repository
cd oceansoulsparkles-admin-repo

# Add GitHub remote (replace YOUR_USERNAME with your GitHub username)
git remote add origin https://github.com/YOUR_USERNAME/oceansoulsparkles-admin.git

# Push to GitHub
git branch -M main
git push -u origin main
```

### 2.2 Push Public Repository

```bash
# Navigate to public repository
cd ../oceansoulsparkles-public-repo

# Add GitHub remote
git remote add origin https://github.com/YOUR_USERNAME/oceansoulsparkles-public.git

# Push to GitHub
git branch -M main
git push -u origin main
```

## 🔒 Step 3: Configure Repository Settings

### 3.1 Admin Repository Security

1. Go to your admin repository on GitHub
2. Click **Settings** → **General**
3. **Security settings**:
   - Enable **"Restrict pushes that create files larger than 100 MB"**
   - Enable **"Restrict pushes that contain private email addresses"**
4. **Branch protection** (Settings → Branches):
   - Add rule for `main` branch
   - Enable **"Require pull request reviews before merging"**
   - Enable **"Dismiss stale PR approvals when new commits are pushed"**

### 3.2 Public Repository Settings

1. Go to your public repository on GitHub
2. Configure similar security settings
3. **Pages** (if you want GitHub Pages backup):
   - Settings → Pages
   - Source: Deploy from a branch
   - Branch: main / (root)

## 🌐 Step 4: Vercel Deployment Setup

### 4.1 Deploy Admin Portal

1. **Go to Vercel.com** and sign in
2. Click **"New Project"**
3. **Import Git Repository**:
   - Select your `oceansoulsparkles-admin` repository
   - Framework Preset: **Next.js**
   - Root Directory: `./` (default)
4. **Environment Variables**:
   - Add all variables from your `.env.local` file
   - **Important**: Use the production values, not development ones
5. **Deploy Settings**:
   - Build Command: `npm run build`
   - Output Directory: `.next` (default)
   - Install Command: `npm ci`
6. Click **"Deploy"**

### 4.2 Deploy Public Website

1. Click **"New Project"** in Vercel
2. **Import Git Repository**:
   - Select your `oceansoulsparkles-public` repository
   - Framework Preset: **Next.js**
   - Root Directory: `./` (default)
3. **Environment Variables**:
   - Add public website environment variables
   - Use read-only database credentials
4. Click **"Deploy"**

## 🌍 Step 5: Custom Domain Configuration

### 5.1 Admin Portal Domain (admin.oceansoulsparkles.com.au)

1. **In Vercel** (Admin project):
   - Go to Settings → Domains
   - Add domain: `admin.oceansoulsparkles.com.au`
   - Copy the CNAME value provided by Vercel

2. **In Squarespace DNS**:
   - Add CNAME record:
     - Type: `CNAME`
     - Host: `admin`
     - Value: `cname.vercel-dns.com` (or the value Vercel provided)
     - TTL: `300`

### 5.2 Public Website Domain (www.oceansoulsparkles.com.au)

1. **In Vercel** (Public project):
   - Go to Settings → Domains
   - Add domain: `www.oceansoulsparkles.com.au`
   - Add domain: `oceansoulsparkles.com.au` (redirect to www)

2. **In Squarespace DNS**:
   - Update existing CNAME record for `www`:
     - Type: `CNAME`
     - Host: `www`
     - Value: `cname.vercel-dns.com`
   - Add A record for root domain:
     - Type: `A`
     - Host: `@`
     - Value: `***********` (Vercel's IP)

## ✅ Step 6: Verification & Testing

### 6.1 Admin Portal Testing

1. **Access the admin portal**: https://admin.oceansoulsparkles.com.au
2. **Test functionality**:
   - Login page loads
   - Database connection works
   - Security headers are present
   - SSL certificate is active

### 6.2 Public Website Testing

1. **Access the public website**: https://www.oceansoulsparkles.com.au
2. **Test functionality**:
   - Homepage loads correctly
   - Booking system works
   - Payment integration functions
   - Mobile responsiveness
   - PWA features

### 6.3 DNS Propagation Check

```bash
# Check DNS resolution
nslookup admin.oceansoulsparkles.com.au
nslookup www.oceansoulsparkles.com.au

# Check SSL certificates
curl -I https://admin.oceansoulsparkles.com.au
curl -I https://www.oceansoulsparkles.com.au
```

## 🔧 Step 7: Environment Variables Setup

### 7.1 Admin Portal Environment Variables

In Vercel → Admin Project → Settings → Environment Variables:

```
NEXT_PUBLIC_ADMIN_SUBDOMAIN=true
NEXT_PUBLIC_SITE_URL=https://admin.oceansoulsparkles.com.au
NEXT_PUBLIC_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzA1OTk5NywiZXhwIjoyMDYyNjM1OTk3fQ._9quzbWREyhdPiQiSUkzuqyBm8v4fkK2uqiswdt3AvY
NEXTAUTH_URL=https://admin.oceansoulsparkles.com.au
NEXTAUTH_SECRET=OSS_Secure_NextAuth_Key_2025_Production_32_Characters_Min
JWT_SECRET=OSS_JWT_Token_Secret_2025_Production_Secure_32_Characters
ENCRYPTION_KEY=OSS_Encrypt_Key_2025_Secure_32Ch
MFA_ENCRYPTION_KEY=OSS_MFA_Encrypt_2025_Secure_32Ch
NEXT_PUBLIC_SQUARE_APPLICATION_ID=*****************************
NEXT_PUBLIC_SQUARE_LOCATION_ID=LBZPW61WHXG6F
SQUARE_ACCESS_TOKEN=****************************************************************
NEXT_PUBLIC_SQUARE_ENVIRONMENT=production
NODE_ENV=production
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_ADMIN_ACCESS=true
```

### 7.2 Public Website Environment Variables

In Vercel → Public Project → Settings → Environment Variables:

```
NEXT_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au
NEXT_PUBLIC_ADMIN_SITE_URL=https://admin.oceansoulsparkles.com.au
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI
NEXT_PUBLIC_SQUARE_APPLICATION_ID=*****************************
NEXT_PUBLIC_SQUARE_LOCATION_ID=LBZPW61WHXG6F
NEXT_PUBLIC_SQUARE_ENVIRONMENT=production
NODE_ENV=production
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_ENABLE_PWA=true
```

## 🚨 Important Security Notes

1. **Never commit `.env.local` files** to GitHub
2. **Admin repository should remain private**
3. **Use different encryption keys** for admin and public
4. **Regularly rotate API keys** and secrets
5. **Monitor access logs** for unusual activity

## 📞 Support

If you encounter issues:
1. Check Vercel deployment logs
2. Verify DNS propagation (can take up to 48 hours)
3. Test environment variables in Vercel dashboard
4. Contact support: <EMAIL>

---

**Repository Setup Complete!** 🎉

Both repositories are now ready for production deployment with proper security and separation.
