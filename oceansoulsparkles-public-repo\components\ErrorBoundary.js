import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details for debugging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // In production, you might want to log this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Log to error service (e.g., Sentry, LogRocket, etc.)
      this.logErrorToService(error, errorInfo);
    }
  }

  logErrorToService = (error, errorInfo) => {
    // Placeholder for error logging service
    // In a real implementation, you would send this to your error tracking service
    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown',
        url: typeof window !== 'undefined' ? window.location.href : 'unknown'
      };

      // Example: Send to error tracking service
      // errorTrackingService.captureException(errorData);
      
      console.warn('Error logged to service:', errorData);
    } catch (loggingError) {
      console.error('Failed to log error to service:', loggingError);
    }
  };

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  render() {
    if (this.state.hasError) {
      // Fallback UI
      return (
        <div style={{
          padding: '40px',
          textAlign: 'center',
          minHeight: '400px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#f8f9fa',
          border: '1px solid #dee2e6',
          borderRadius: '8px',
          margin: '20px',
          fontFamily: 'Arial, sans-serif'
        }}>
          <div style={{
            fontSize: '48px',
            marginBottom: '20px',
            color: '#6c757d'
          }}>
            🌊
          </div>
          
          <h2 style={{
            color: '#495057',
            marginBottom: '16px',
            fontSize: '24px'
          }}>
            Oops! Something went wrong
          </h2>
          
          <p style={{
            color: '#6c757d',
            marginBottom: '24px',
            maxWidth: '500px',
            lineHeight: '1.5'
          }}>
            We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.
          </p>

          <div style={{ marginBottom: '20px' }}>
            <button
              onClick={this.handleRetry}
              style={{
                backgroundColor: '#3788d8',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '6px',
                fontSize: '16px',
                cursor: 'pointer',
                marginRight: '12px',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#2c6cb7'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#3788d8'}
            >
              Try Again
            </button>
            
            <a
              href="/"
              style={{
                backgroundColor: '#6c757d',
                color: 'white',
                textDecoration: 'none',
                padding: '12px 24px',
                borderRadius: '6px',
                fontSize: '16px',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#5a6268'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#6c757d'}
            >
              Go Home
            </a>
          </div>

          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details style={{
              marginTop: '20px',
              padding: '16px',
              backgroundColor: '#fff3cd',
              border: '1px solid #ffeaa7',
              borderRadius: '4px',
              maxWidth: '600px',
              textAlign: 'left'
            }}>
              <summary style={{
                cursor: 'pointer',
                fontWeight: 'bold',
                marginBottom: '8px',
                color: '#856404'
              }}>
                Error Details (Development Only)
              </summary>
              <pre style={{
                fontSize: '12px',
                color: '#856404',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                {this.state.error && this.state.error.toString()}
                <br />
                {this.state.errorInfo.componentStack}
              </pre>
            </details>
          )}

          <p style={{
            fontSize: '14px',
            color: '#adb5bd',
            marginTop: '20px'
          }}>
            If this problem persists, please contact us at{' '}
            <a 
              href="mailto:<EMAIL>"
              style={{ color: '#3788d8' }}
            >
              <EMAIL>
            </a>
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
