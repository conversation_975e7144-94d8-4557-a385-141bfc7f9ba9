{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KvAVFQ+ez9L5FyAo/AQPuyIo4Kj9y+DvCs5odcZKxw8="}}}, "functions": {}, "sortedMiddleware": ["/"]}