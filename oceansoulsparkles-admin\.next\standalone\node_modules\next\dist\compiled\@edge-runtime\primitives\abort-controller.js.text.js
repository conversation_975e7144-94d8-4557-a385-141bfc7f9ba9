module.exports = "\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/primitives/abort-controller.js\nvar abort_controller_exports = {};\n__export(abort_controller_exports, {\n  AbortController: () => AbortController,\n  AbortSignal: () => AbortSignal,\n  DOMException: () => DOMException\n});\nmodule.exports = __toCommonJS(abort_controller_exports);\nvar kSignal = Symbol(\"kSignal\");\nvar kAborted = Symbol(\"kAborted\");\nvar kReason = Symbol(\"kReason\");\nvar kName = Symbol(\"kName\");\nvar kOnabort = Symbol(\"kOnabort\");\nvar _DOMException = class _DOMException extends Error {\n  constructor(message, name) {\n    super(message);\n    this[kName] = name;\n  }\n  get name() {\n    return this[kName];\n  }\n};\n__name(_DOMException, \"DOMException\");\nvar DOMException = _DOMException;\nfunction createAbortSignal() {\n  const signal = new EventTarget();\n  Object.setPrototypeOf(signal, AbortSignal.prototype);\n  signal[kAborted] = false;\n  signal[kReason] = void 0;\n  signal[kOnabort] = void 0;\n  return signal;\n}\n__name(createAbortSignal, \"createAbortSignal\");\nfunction abortSignalAbort(signal, reason) {\n  if (typeof reason === \"undefined\") {\n    reason = new DOMException(\"This operation was aborted\", \"AbortError\");\n  }\n  if (signal.aborted) {\n    return;\n  }\n  signal[kReason] = reason;\n  signal[kAborted] = true;\n  signal.dispatchEvent(new Event(\"abort\"));\n}\n__name(abortSignalAbort, \"abortSignalAbort\");\nvar _AbortController = class _AbortController {\n  constructor() {\n    this[kSignal] = createAbortSignal();\n  }\n  get signal() {\n    return this[kSignal];\n  }\n  abort(reason) {\n    abortSignalAbort(this.signal, reason);\n  }\n};\n__name(_AbortController, \"AbortController\");\nvar AbortController = _AbortController;\nvar _AbortSignal = class _AbortSignal extends EventTarget {\n  constructor() {\n    throw new TypeError(\"Illegal constructor\");\n  }\n  get aborted() {\n    return this[kAborted];\n  }\n  get reason() {\n    return this[kReason];\n  }\n  get onabort() {\n    return this[kOnabort];\n  }\n  set onabort(value) {\n    if (this[kOnabort]) {\n      this.removeEventListener(\"abort\", this[kOnabort]);\n    }\n    if (value) {\n      this[kOnabort] = value;\n      this.addEventListener(\"abort\", this[kOnabort]);\n    }\n  }\n  throwIfAborted() {\n    if (this[kAborted]) {\n      throw this[kReason];\n    }\n  }\n  static abort(reason) {\n    const signal = createAbortSignal();\n    abortSignalAbort(signal, reason);\n    return signal;\n  }\n  static timeout(milliseconds) {\n    const signal = createAbortSignal();\n    setTimeout(() => {\n      abortSignalAbort(\n        signal,\n        new DOMException(\n          \"The operation was aborted due to timeout\",\n          \"TimeoutError\"\n        )\n      );\n    }, milliseconds);\n    return signal;\n  }\n};\n__name(_AbortSignal, \"AbortSignal\");\nvar AbortSignal = _AbortSignal;\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  AbortController,\n  AbortSignal,\n  DOMException\n});\n"