// Public Subdomain Middleware - Security for public-facing website only
// This middleware ensures NO admin functionality is accessible

import { NextResponse } from 'next/server';

/**
 * Middleware configuration - Apply to all routes for security
 */
export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|images|manifest.json|sw.js|robots.txt|sitemap.xml).*)',
  ],
};

/**
 * Public subdomain middleware function
 * Enforces strict security for public-only access
 */
export default async function middleware(req) {
  const { pathname } = req.nextUrl;
  const hostname = req.headers.get('host');

  // Block all admin routes completely
  if (pathname.startsWith('/admin')) {
    console.warn(`[Public Security] Blocked admin access attempt: ${pathname} from ${req.ip}`);
    return new Response('Not Found', { status: 404 });
  }

  // Block admin API routes
  if (pathname.startsWith('/api/admin')) {
    console.warn(`[Public Security] Blocked admin API access attempt: ${pathname} from ${req.ip}`);
    return new Response('Not Found', { status: 404 });
  }

  // Block staff routes
  if (pathname.startsWith('/staff')) {
    console.warn(`[Public Security] Blocked staff access attempt: ${pathname} from ${req.ip}`);
    return new Response('Not Found', { status: 404 });
  }

  // Block artist routes (these should be on staff subdomain)
  if (pathname.startsWith('/artist')) {
    console.warn(`[Public Security] Blocked artist access attempt: ${pathname} from ${req.ip}`);
    return new Response('Not Found', { status: 404 });
  }

  // Block apply routes (these should be on staff subdomain)
  if (pathname.startsWith('/apply')) {
    console.warn(`[Public Security] Blocked application access attempt: ${pathname} from ${req.ip}`);
    return new Response('Not Found', { status: 404 });
  }

  // Allow only public API routes
  if (pathname.startsWith('/api/')) {
    const allowedPublicAPIs = [
      '/api/public/',
      '/api/bookings/create',
      '/api/bookings/check-availability',
      '/api/checkout/',
      '/api/customer/',
      '/api/auth/customer',
      '/api/notifications/customer',
      '/api/apple-pay/',
      '/api/health',
    ];

    const isAllowedAPI = allowedPublicAPIs.some(api => pathname.startsWith(api));
    
    if (!isAllowedAPI) {
      console.warn(`[Public Security] Blocked unauthorized API access: ${pathname} from ${req.ip}`);
      return new Response('Not Found', { status: 404 });
    }
  }

  // Add security headers to all responses
  const response = NextResponse.next();

  // Security headers for public site
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // Prevent admin subdomain access
  if (hostname?.includes('admin.') || hostname?.includes('staff.')) {
    console.warn(`[Public Security] Blocked subdomain access attempt from public: ${hostname}`);
    return new Response('Not Found', { status: 404 });
  }

  // Add CORS headers for public APIs
  if (pathname.startsWith('/api/')) {
    response.headers.set('Access-Control-Allow-Origin', 
      process.env.NODE_ENV === 'production' 
        ? 'https://www.oceansoulsparkles.com.au' 
        : '*'
    );
    response.headers.set('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  }

  return response;
}
