import { useEffect } from 'react';

export function OneSignalProvider({ children }) {
  useEffect(() => {
    // Only initialize OneSignal in production and if app ID is provided
    if (process.env.NODE_ENV === 'production' && 
        process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID &&
        typeof window !== 'undefined') {
      
      // Initialize OneSignal for customer notifications only
      initializeOneSignal();
    }
  }, []);

  const initializeOneSignal = async () => {
    try {
      // Dynamically import OneSignal to avoid SSR issues
      const OneSignal = (await import('react-onesignal')).default;
      
      await OneSignal.init({
        appId: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID,
        safari_web_id: process.env.NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID,
        notifyButton: {
          enable: false, // We'll handle the prompt ourselves
        },
        allowLocalhostAsSecureOrigin: process.env.NODE_ENV === 'development',
        autoRegister: false, // We'll register manually after user consent
        autoResubscribe: true,
        persistNotification: false,
        promptOptions: {
          slidedown: {
            enabled: false, // We use our custom prompt
          },
          fullscreen: {
            enabled: false,
          },
        },
        welcomeNotification: {
          disable: true, // We'll send our own welcome notification
        },
        notificationClickHandlerMatch: 'origin',
        notificationClickHandlerAction: 'navigate',
      });

      console.log('[OneSignal] Initialized for customer notifications');

      // Set up event listeners for customer-specific notifications
      OneSignal.on('subscriptionChange', function(isSubscribed) {
        console.log('[OneSignal] Subscription changed:', isSubscribed);
        
        if (isSubscribed) {
          // Tag user as customer
          OneSignal.sendTag('user_type', 'customer');
          OneSignal.sendTag('source', 'public_website');
        }
      });

      OneSignal.on('notificationPermissionChange', function(permission) {
        console.log('[OneSignal] Permission changed:', permission);
      });

    } catch (error) {
      console.error('[OneSignal] Initialization failed:', error);
    }
  };

  return children;
}

export default OneSignalProvider;
