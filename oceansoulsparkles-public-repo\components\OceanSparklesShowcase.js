import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function OceanSparklesShowcase({
  title,
  subtitle,
  ctaText,
  ctaLink
}) {
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const showcaseItems = [
    {
      title: "Face Painting Magic",
      description: "Transform into your favorite character with our professional face painting services",
      image: "/images/showcase/face-painting.jpg",
      color: "#FF6B6B"
    },
    {
      title: "Airbrush Artistry",
      description: "Stunning airbrush body art that brings your imagination to life",
      image: "/images/showcase/airbrush-art.jpg",
      color: "#4ECDC4"
    },
    {
      title: "Festival Braiding",
      description: "Colorful braids and hair art perfect for festivals and special events",
      image: "/images/showcase/braiding.jpg",
      color: "#45B7D1"
    },
    {
      title: "Eco-Friendly Glitter",
      description: "Biodegradable glitter that sparkles without harming the environment",
      image: "/images/showcase/eco-glitter.jpg",
      color: "#96CEB4"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % showcaseItems.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [showcaseItems.length]);

  const showcaseStyle = {
    padding: '80px 0',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    position: 'relative',
    overflow: 'hidden'
  };

  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 20px',
    position: 'relative',
    zIndex: 2
  };

  const headerStyle = {
    textAlign: 'center',
    marginBottom: '60px'
  };

  const titleStyle = {
    fontSize: 'clamp(2rem, 4vw, 3rem)',
    fontWeight: '700',
    marginBottom: '16px',
    background: 'linear-gradient(135deg, #ffffff, #f0f0f0)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text'
  };

  const subtitleStyle = {
    fontSize: 'clamp(1rem, 2vw, 1.2rem)',
    color: 'rgba(255, 255, 255, 0.9)',
    maxWidth: '600px',
    margin: '0 auto'
  };

  const slidesContainerStyle = {
    position: 'relative',
    height: '400px',
    borderRadius: '20px',
    overflow: 'hidden',
    marginBottom: '40px',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)'
  };

  const slideStyle = (index) => ({
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: `linear-gradient(135deg, ${showcaseItems[index].color}aa, ${showcaseItems[index].color}dd)`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '40px',
    opacity: index === currentSlide ? 1 : 0,
    transform: index === currentSlide ? 'translateX(0)' : 'translateX(100%)',
    transition: 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)'
  });

  const slideContentStyle = {
    flex: 1,
    paddingRight: '40px'
  };

  const slideImageStyle = {
    width: '300px',
    height: '250px',
    borderRadius: '16px',
    objectFit: 'cover',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)'
  };

  const indicatorsStyle = {
    display: 'flex',
    justifyContent: 'center',
    gap: '12px',
    marginBottom: '40px'
  };

  const indicatorStyle = (index) => ({
    width: '12px',
    height: '12px',
    borderRadius: '50%',
    background: index === currentSlide ? 'white' : 'rgba(255, 255, 255, 0.4)',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    transform: index === currentSlide ? 'scale(1.2)' : 'scale(1)'
  });

  const ctaStyle = {
    textAlign: 'center'
  };

  const ctaButtonStyle = {
    display: 'inline-flex',
    alignItems: 'center',
    gap: '8px',
    padding: '16px 32px',
    background: 'rgba(255, 255, 255, 0.2)',
    color: 'white',
    textDecoration: 'none',
    borderRadius: '50px',
    fontSize: '18px',
    fontWeight: '600',
    transition: 'all 0.3s ease',
    border: '2px solid rgba(255, 255, 255, 0.3)',
    backdropFilter: 'blur(10px)'
  };

  return (
    <section style={showcaseStyle}>
      {/* Background decoration */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'url("/images/sparkle-pattern.png") repeat',
        opacity: 0.1,
        zIndex: 1
      }} />

      <div style={containerStyle}>
        <div style={headerStyle}>
          <h2 style={titleStyle}>{title}</h2>
          <p style={subtitleStyle}>{subtitle}</p>
        </div>

        <div style={slidesContainerStyle}>
          {showcaseItems.map((item, index) => (
            <div key={index} style={slideStyle(index)}>
              <div style={slideContentStyle}>
                <h3 style={{
                  fontSize: '2rem',
                  fontWeight: '700',
                  marginBottom: '16px',
                  color: 'white'
                }}>
                  {item.title}
                </h3>
                <p style={{
                  fontSize: '1.1rem',
                  color: 'rgba(255, 255, 255, 0.95)',
                  lineHeight: '1.6'
                }}>
                  {item.description}
                </p>
              </div>
              <img 
                src={item.image} 
                alt={item.title}
                style={slideImageStyle}
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
            </div>
          ))}
        </div>

        <div style={indicatorsStyle}>
          {showcaseItems.map((_, index) => (
            <button
              key={index}
              style={indicatorStyle(index)}
              onClick={() => setCurrentSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>

        {ctaText && ctaLink && (
          <div style={ctaStyle}>
            <Link href={ctaLink} style={ctaButtonStyle}>
              {ctaText}
              <span style={{ fontSize: '20px' }}>✨</span>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}
