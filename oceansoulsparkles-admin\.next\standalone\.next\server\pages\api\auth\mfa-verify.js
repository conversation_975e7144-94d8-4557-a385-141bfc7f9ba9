"use strict";(()=>{var e={};e.id=16,e.ids=[16],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},3829:(e,t,r)=>{r.r(t),r.d(t,{config:()=>m,default:()=>l,routeModule:()=>c});var s={};r.r(s),r.d(s,{default:()=>d});var a=r(1802),n=r(7153),o=r(8781),i=r(7474),u=r(1775);async function d(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let r=await (0,u.ge)(e);if(!r.allowed)return t.status(429).json({error:"Too many MFA attempts. Please try again later.",resetTime:r.resetTime});let{userId:s,mfaCode:a}=e.body;if(!s||!a)return t.status(400).json({error:"User ID and MFA code are required"});if(!/^\d{6}$/.test(a))return t.status(400).json({error:"MFA code must be 6 digits"});let n=e.headers["x-forwarded-for"]||e.headers["x-real-ip"]||e.connection.remoteAddress||"unknown",o=await (0,i.cm)(s,a,n);if(!o.success)return t.status(401).json({error:o.error});return o.token&&t.setHeader("Set-Cookie",[`admin-token=${o.token}; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=28800`]),t.status(200).json({success:!0,token:o.token,user:o.user})}catch(e){return console.error("MFA verification API error:",e),t.status(500).json({error:"Internal server error"})}}let l=(0,o.l)(s,"default"),m=(0,o.l)(s,"config"),c=new a.PagesAPIRouteModule({definition:{kind:n.x.PAGES_API,page:"/api/auth/mfa-verify",pathname:"/api/auth/mfa-verify",bundlePath:"",filename:""},userland:s})},1775:(e,t,r)=>{r.d(t,{ge:()=>n});let s=new Map,a={"/api/auth/login":{windowMs:9e5,maxRequests:5},"/api/auth/mfa":{windowMs:3e5,maxRequests:10},"/api/auth/forgot-password":{windowMs:36e5,maxRequests:3},"/api/admin":{windowMs:6e4,maxRequests:100},default:{windowMs:6e4,maxRequests:60}};async function n(e){let t=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return e.headers.get("cf-connecting-ip")||(t?t.split(",")[0].trim():r||e.ip||"unknown")}(e),r=e.nextUrl.pathname,n=function(e){if(a[e])return a[e];for(let[t,r]of Object.entries(a))if("default"!==t&&e.startsWith(t))return r;return a.default}(r),o=`${t}:${r}`,i=Date.now();n.windowMs;let u=s.get(o);return(u&&u.resetTime<i&&(s.delete(o),u=void 0),u||(u={count:0,resetTime:i+n.windowMs}),u.count>=n.maxRequests)?{allowed:!1,ip:t,remaining:0,resetTime:u.resetTime,reason:"Rate limit exceeded"}:(u.count++,s.set(o,u),.01>Math.random()&&function(){let e=Date.now(),t=[];s.forEach((r,s)=>{r.resetTime<e&&t.push(s)}),t.forEach(e=>s.delete(e))}(),{allowed:!0,ip:t,remaining:n.maxRequests-u.count,resetTime:u.resetTime})}}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[805],()=>r(3829));module.exports=s})();