(()=>{"use strict";var e={56:e=>{
/*!
 * bytes
 * Copyright(c) 2012-2014 <PERSON><PERSON>
 * Copyright(c) 2015 <PERSON>
 * MIT Licensed
 */
e.exports=bytes;e.exports.format=format;e.exports.parse=parse;var r=/\B(?=(\d{3})+(?!\d))/g;var a=/(?:\.0*|(\.[^0]+)0+)$/;var t={b:1,kb:1<<10,mb:1<<20,gb:1<<30,tb:Math.pow(1024,4),pb:Math.pow(1024,5)};var i=/^((-|\+)?(\d+(?:\.\d+)?)) *(kb|mb|gb|tb|pb)$/i;function bytes(e,r){if(typeof e==="string"){return parse(e)}if(typeof e==="number"){return format(e,r)}return null}function format(e,i){if(!Number.isFinite(e)){return null}var n=Math.abs(e);var o=i&&i.thousandsSeparator||"";var s=i&&i.unitSeparator||"";var f=i&&i.decimalPlaces!==undefined?i.decimalPlaces:2;var u=Boolean(i&&i.fixedDecimals);var p=i&&i.unit||"";if(!p||!t[p.toLowerCase()]){if(n>=t.pb){p="PB"}else if(n>=t.tb){p="TB"}else if(n>=t.gb){p="GB"}else if(n>=t.mb){p="MB"}else if(n>=t.kb){p="KB"}else{p="B"}}var b=e/t[p.toLowerCase()];var l=b.toFixed(f);if(!u){l=l.replace(a,"$1")}if(o){l=l.split(".").map((function(e,a){return a===0?e.replace(r,o):e})).join(".")}return l+s+p}function parse(e){if(typeof e==="number"&&!isNaN(e)){return e}if(typeof e!=="string"){return null}var r=i.exec(e);var a;var n="b";if(!r){a=parseInt(e,10);n="b"}else{a=parseFloat(r[1]);n=r[4].toLowerCase()}return Math.floor(t[n]*a)}}};var r={};function __nccwpck_require__(a){var t=r[a];if(t!==undefined){return t.exports}var i=r[a]={exports:{}};var n=true;try{e[a](i,i.exports,__nccwpck_require__);n=false}finally{if(n)delete r[a]}return i.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var a=__nccwpck_require__(56);module.exports=a})();