import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

export default function PWAProvider({ children }) {
  const [isOnline, setIsOnline] = useState(true);
  const [installPrompt, setInstallPrompt] = useState(null);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    if (typeof window !== 'undefined') {
      setIsInstalled(window.matchMedia('(display-mode: standalone)').matches);
    }

    // Handle online/offline status
    const handleOnline = () => {
      setIsOnline(true);
      toast.success('Connection restored!', {
        position: 'bottom-center',
        autoClose: 3000
      });
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast.warning('You are offline. Some features may not work.', {
        position: 'bottom-center',
        autoClose: 5000
      });
    };

    // Handle PWA install prompt
    const handleBeforeInstallPrompt = (e) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      // Save the event so it can be triggered later
      setInstallPrompt(e);
    };

    // Handle app installed
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setInstallPrompt(null);
      toast.success('Ocean Soul Sparkles app installed successfully!', {
        position: 'bottom-center',
        autoClose: 5000
      });
    };

    // Register service worker
    if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
          
          // Handle service worker updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New content is available
                toast.info('New content available! Refresh to update.', {
                  position: 'bottom-center',
                  autoClose: 8000,
                  onClick: () => window.location.reload()
                });
              }
            });
          });
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    }

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // Set initial online status
    setIsOnline(navigator.onLine);

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // Install PWA function
  const installPWA = async () => {
    if (!installPrompt) return;

    try {
      // Show the install prompt
      installPrompt.prompt();
      
      // Wait for the user to respond to the prompt
      const { outcome } = await installPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }
      
      // Clear the prompt
      setInstallPrompt(null);
    } catch (error) {
      console.error('Error installing PWA:', error);
    }
  };

  return (
    <>
      {children}
      
      {/* PWA Install Prompt */}
      {installPrompt && !isInstalled && (
        <div style={{
          position: 'fixed',
          bottom: '20px',
          left: '20px',
          right: '20px',
          backgroundColor: '#3788d8',
          color: 'white',
          padding: '16px',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          maxWidth: '400px',
          margin: '0 auto'
        }}>
          <div style={{ flex: 1, marginRight: '12px' }}>
            <strong>Install Ocean Soul Sparkles</strong>
            <br />
            <small>Get quick access to our services!</small>
          </div>
          <div>
            <button
              onClick={installPWA}
              style={{
                backgroundColor: 'white',
                color: '#3788d8',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '4px',
                fontSize: '14px',
                fontWeight: 'bold',
                cursor: 'pointer',
                marginRight: '8px'
              }}
            >
              Install
            </button>
            <button
              onClick={() => setInstallPrompt(null)}
              style={{
                backgroundColor: 'transparent',
                color: 'white',
                border: '1px solid white',
                padding: '8px 16px',
                borderRadius: '4px',
                fontSize: '14px',
                cursor: 'pointer'
              }}
            >
              Later
            </button>
          </div>
        </div>
      )}

      {/* Offline Indicator */}
      {!isOnline && (
        <div style={{
          position: 'fixed',
          top: '0',
          left: '0',
          right: '0',
          backgroundColor: '#dc3545',
          color: 'white',
          padding: '8px',
          textAlign: 'center',
          fontSize: '14px',
          zIndex: 1001
        }}>
          You are currently offline. Some features may not work properly.
        </div>
      )}
    </>
  );
}
