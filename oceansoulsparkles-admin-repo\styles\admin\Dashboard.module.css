/* Admin Dashboard Styles */
.dashboard {
  padding: 0;
  max-width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--admin-spacing-xl);
  padding: var(--admin-spacing-lg);
  background: linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%);
  border-radius: var(--admin-radius-lg);
  color: white;
}

.welcomeSection h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 var(--admin-spacing-sm) 0;
  background: linear-gradient(135deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcomeSection p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

.headerActions {
  display: flex;
  gap: var(--admin-spacing-md);
}

.refreshButton {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  border-radius: var(--admin-radius-md);
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.refreshButton:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.refreshButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--admin-gray);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--admin-border-light);
  border-top: 4px solid var(--admin-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--admin-spacing-md);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loadingGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--admin-spacing-lg);
  margin-bottom: var(--admin-spacing-xl);
}

.loadingCard {
  height: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--admin-radius-lg);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.dashboardGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--admin-spacing-xl);
}

.statsSection {
  grid-column: 1 / -1;
}

.quickActionsSection {
  grid-column: 1 / -1;
}

.bookingsSection {
  grid-column: 1 / -1;
}

.activitySection {
  grid-column: 1 / -1;
}

.artistSection {
  background: var(--admin-bg-primary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-xl);
  border: 1px solid var(--admin-border-light);
  margin-top: var(--admin-spacing-xl);
}

.sectionHeader {
  text-align: center;
  margin-bottom: var(--admin-spacing-xl);
}

.sectionHeader h2 {
  color: var(--admin-darker);
  font-size: 2rem;
  margin-bottom: var(--admin-spacing-sm);
}

.sectionHeader p {
  color: var(--admin-gray);
  font-size: 1.1rem;
  margin: 0;
}

.performanceGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--admin-spacing-lg);
}

.performanceCard {
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-lg);
  text-align: center;
  border: 1px solid var(--admin-border-light);
  transition: transform var(--admin-transition-normal);
}

.performanceCard:hover {
  transform: translateY(-2px);
}

.performanceCard h3 {
  color: var(--admin-gray);
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--admin-spacing-md);
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--admin-spacing-xs);
}

.value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--admin-primary);
  line-height: 1;
}

.label {
  font-size: 0.85rem;
  color: var(--admin-gray);
  font-weight: 500;
}

.adminSection {
  background: var(--admin-bg-primary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-xl);
  border: 1px solid var(--admin-border-light);
  margin-top: var(--admin-spacing-xl);
}

.systemGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--admin-spacing-lg);
}

.systemCard {
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-lg);
  text-align: center;
  border: 1px solid var(--admin-border-light);
}

.systemCard h3 {
  color: var(--admin-gray);
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--admin-spacing-md);
}

.statusIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-spacing-sm);
  font-weight: 600;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.statusHealthy {
  background: var(--admin-success);
}

/* Large Desktop Layout */
@media (min-width: 1200px) {
  .dashboardGrid {
    grid-template-columns: 2fr 1fr;
    grid-template-rows: auto auto auto;
  }

  .statsSection {
    grid-column: 1 / -1;
    grid-row: 1;
  }

  .quickActionsSection {
    grid-column: 2;
    grid-row: 2;
  }

  .bookingsSection {
    grid-column: 1;
    grid-row: 2 / 4;
  }

  .activitySection {
    grid-column: 2;
    grid-row: 3;
  }
}

/* Tablet Layout */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: var(--admin-spacing-md);
    text-align: center;
  }

  .welcomeSection h1 {
    font-size: 2rem;
  }

  .performanceGrid,
  .systemGrid {
    grid-template-columns: 1fr;
    gap: var(--admin-spacing-md);
  }

  .artistSection,
  .adminSection {
    padding: var(--admin-spacing-lg);
  }
}

/* Mobile Layout */
@media (max-width: 480px) {
  .header {
    padding: var(--admin-spacing-md);
  }

  .welcomeSection h1 {
    font-size: 1.75rem;
  }

  .welcomeSection p {
    font-size: 1rem;
  }

  .dashboardGrid {
    gap: var(--admin-spacing-lg);
  }

  .artistSection,
  .adminSection {
    padding: var(--admin-spacing-md);
  }

  .sectionHeader h2 {
    font-size: 1.5rem;
  }

  .performanceCard,
  .systemCard {
    padding: var(--admin-spacing-md);
  }

  .value {
    font-size: 1.5rem;
  }
}
