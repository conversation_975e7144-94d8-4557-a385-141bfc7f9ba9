const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['www.oceansoulsparkles.com.au', 'static.wixstatic.com'],
  },
  
  // Public-only security headers
  async headers() {
    const isProduction = process.env.NODE_ENV === 'production';
    const enableSecurityHeaders = process.env.NEXT_PUBLIC_ENABLE_SECURITY_HEADERS === 'true';

    if (!enableSecurityHeaders) {
      return [];
    }

    return [
      {
        // Apply security headers to all routes
        source: '/(.*)',
        headers: [
          // CORS Headers - Restrictive for public site
          {
            key: 'Access-Control-Allow-Origin',
            value: isProduction ? 'https://www.oceansoulsparkles.com.au' : '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET,POST,OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
          // Security Headers
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          // Content Security Policy - Restrictive for public site
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.onesignal.com https://js.squareup.com",
              "style-src 'self' 'unsafe-inline'",
              "img-src 'self' data: https:",
              "font-src 'self' data:",
              "connect-src 'self' https://ndlgbcsbidyhxbpqzgqp.supabase.co https://api.squareup.com https://pci-connect.squareup.com",
              "frame-src 'self' https://js.squareup.com",
            ].join('; '),
          },
        ],
      },
      {
        // Block admin routes completely
        source: '/admin/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
        ],
      },
    ];
  },

  // Redirects - Block admin access
  async redirects() {
    return [
      // Redirect HTTP to HTTPS
      {
        source: '/(.*)',
        has: [
          {
            type: 'header',
            key: 'x-forwarded-proto',
            value: 'http',
          },
        ],
        destination: 'https://www.oceansoulsparkles.com.au/$1',
        permanent: true,
      },
      // Redirect non-www to www
      {
        source: '/(.*)',
        has: [
          {
            type: 'host',
            value: 'oceansoulsparkles.com.au',
          },
        ],
        destination: 'https://www.oceansoulsparkles.com.au/$1',
        permanent: true,
      },
      // Block all admin routes - redirect to 404
      {
        source: '/admin/:path*',
        destination: '/404',
        permanent: false,
      },
    ];
  },

  // Rewrites for Apple Pay domain verification
  async rewrites() {
    return [
      {
        source: '/.well-known/apple-developer-merchantid-domain-association',
        destination: '/api/apple-pay/domain-association',
      },
    ];
  },

  webpack: (config, { isServer }) => {
    // Add support for importing SVGs as React components
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    // Add path aliases
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname),
    };

    if (!isServer) {
      // Don't attempt to import Node.js built-in modules on the client side
      config.resolve.fallback = {
        fs: false,
        path: false,
        util: false,
        crypto: false,
      };
    }

    return config;
  },
};

module.exports = nextConfig;
