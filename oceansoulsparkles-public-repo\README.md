# Ocean Soul Sparkles - Public Website

**Customer-facing booking and information portal for Ocean Soul Sparkles**

## 🌊 Overview

This is the public website for Ocean Soul Sparkles, Australia's premier body art and hair braiding service. Customers can browse services, view galleries, make bookings, and purchase gift cards through this platform.

## ✨ Features

### Customer Experience
- **Service Browsing**: Explore our 6 service categories with detailed descriptions
- **Online Booking**: Book appointments for events and individual services
- **Gallery**: View our portfolio of body art, face painting, and hair braiding
- **Gift Cards**: Purchase and redeem digital gift cards
- **Mobile Responsive**: Optimized for all devices
- **PWA Support**: Install as a mobile app

### Service Categories
1. **Special Events**: Custom packages for parties and events
2. **Body Painting**: Professional body art and UV painting
3. **Airbrush**: Airbrush tattoos and body art
4. **Face Painting**: Creative face designs for all ages
5. **Glitter & Gems**: Sparkle applications and gem art
6. **Hair & Braiding**: Professional hair braiding and styling

### Business Features
- **SEO Optimized**: Full search engine optimization
- **Analytics Integration**: Google Analytics and performance tracking
- **Social Media Integration**: Connected to Instagram and Facebook
- **Contact Forms**: Multiple contact methods for inquiries
- **Location Services**: Service area information for Australia

## 🚀 Deployment

### Production URL
- **Public Website**: https://www.oceansoulsparkles.com.au
- **Admin Portal**: https://admin.oceansoulsparkles.com.au

### Quick Deploy to Vercel

```bash
# Clone the repository
git clone https://github.com/YOUR_USERNAME/oceansoulsparkles-public.git
cd oceansoulsparkles-public

# Install dependencies
npm install

# Deploy to production
vercel --prod
```

### Environment Configuration

Create `.env.local` with the following variables:

```env
# Public Website Configuration
NEXT_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au
NEXT_PUBLIC_ADMIN_SITE_URL=https://admin.oceansoulsparkles.com.au

# Database (Supabase) - Read-only access
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key

# Payment Processing (Square)
NEXT_PUBLIC_SQUARE_APPLICATION_ID=your_square_app_id
NEXT_PUBLIC_SQUARE_LOCATION_ID=your_location_id
NEXT_PUBLIC_SQUARE_ENVIRONMENT=production

# Analytics & Tracking
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga_id
NEXT_PUBLIC_FACEBOOK_PIXEL_ID=your_fb_pixel_id

# OneSignal Notifications
NEXT_PUBLIC_ONESIGNAL_APP_ID=your_onesignal_app_id

# Production Settings
NODE_ENV=production
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_ENABLE_PWA=true
```

## 🏗️ Architecture

### Tech Stack
- **Framework**: Next.js 14
- **Language**: JavaScript/TypeScript
- **Database**: Supabase (PostgreSQL) - Read-only
- **Payments**: Square Web Payments SDK
- **Hosting**: Vercel
- **Styling**: CSS Modules + Custom CSS
- **PWA**: next-pwa for offline support

### Project Structure
```
oceansoulsparkles-public/
├── components/          # React components
│   ├── booking/        # Booking-related components
│   ├── gallery/        # Gallery and portfolio
│   ├── payment/        # Payment processing
│   └── ui/             # Reusable UI components
├── pages/              # Next.js pages
│   ├── api/            # API routes (public only)
│   ├── book/           # Booking pages
│   ├── gallery/        # Gallery pages
│   └── shop/           # Gift card shop
├── lib/                # Utility libraries
├── styles/             # CSS modules and styles
├── public/             # Static assets
├── contexts/           # React contexts
└── scripts/            # Build and utility scripts
```

## 📱 Progressive Web App (PWA)

The website includes PWA functionality:
- **Offline Support**: Basic functionality works offline
- **Install Prompt**: Users can install as a mobile app
- **Push Notifications**: OneSignal integration for booking reminders
- **App-like Experience**: Native app feel on mobile devices

## 🎨 Services Offered

### Event Services (2-6 hours)
- Birthday parties and celebrations
- Corporate events and team building
- Festivals and community events
- Wedding parties and bridal events
- School events and fundraisers

### Individual Services (30 minutes - 2 hours)
- Custom body art and painting
- Professional face painting
- Hair braiding and styling
- Glitter and gem applications
- Airbrush temporary tattoos

### Pricing Tiers
- **Basic**: Entry-level services
- **Standard**: Most popular option
- **Premium**: Enhanced experience
- **Deluxe**: Full-service packages

## 🛒 E-commerce Features

### Gift Cards
- Digital gift card purchases
- Custom amounts and designs
- Email delivery to recipients
- Secure redemption system
- Expiration date management

### Booking System
- Real-time availability checking
- Service customization options
- Artist/braider selection
- Location and travel options
- Payment processing integration

## 📊 Analytics & Performance

### Tracking
- Google Analytics 4 integration
- Facebook Pixel for advertising
- Custom event tracking
- Performance monitoring
- User behavior analysis

### SEO Features
- Structured data markup
- Meta tag optimization
- Sitemap generation
- Image optimization
- Core Web Vitals optimization

## 🔧 Development

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account (read-only access)
- Square developer account

### Local Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Generate sitemap
npm run generate-sitemap

# Optimize images
npm run optimize-images
```

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run generate-sitemap` - Generate sitemap.xml
- `npm run optimize-images` - Optimize images for web

## 📚 Documentation

- **[Deployment Guide](./DEPLOYMENT.md)** - Complete deployment instructions
- **[Implementation Guide](./IMPLEMENTATION_GUIDE.md)** - Technical implementation details
- **[Production Checklist](./PRODUCTION-CHECKLIST.md)** - Pre-launch checklist

## 🌐 Domain & DNS

### Primary Domain
- **Main Website**: www.oceansoulsparkles.com.au
- **Redirect**: oceansoulsparkles.com.au → www.oceansoulsparkles.com.au

### DNS Configuration
Managed through Squarespace DNS with CNAME records pointing to Vercel.

## 📞 Support & Contact

### Business Information
- **Business Name**: Ocean Soul Sparkles
- **Location**: Australia-wide service
- **Email**: <EMAIL>
- **Phone**: [Contact details on website]

### Technical Support
- **Email**: <EMAIL>
- **Issues**: Use GitHub issues for technical problems

## 📄 License

This project is proprietary software owned by Ocean Soul Sparkles. All rights reserved.

## 🚀 Deployment Status

**Current Status**: ✅ Ready for Production

- ✅ PWA functionality enabled
- ✅ SEO optimization complete
- ✅ Payment integration tested
- ✅ Mobile responsive design
- ✅ Performance optimized
- ✅ Analytics configured

---

**Ocean Soul Sparkles Public Website v1.0.0**  
*Beautiful. Functional. Engaging.*
