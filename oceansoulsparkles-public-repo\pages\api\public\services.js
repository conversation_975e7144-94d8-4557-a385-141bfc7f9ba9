import { publicData } from '@/lib/supabase';

/**
 * Public API endpoint for fetching services
 * This endpoint provides services data for the public website
 * No authentication required - only returns active services visible to public
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('[Public API] Fetching services for public display');

    // Fetch active services using public data access
    const { data: services, error } = await publicData.getServices();

    if (error) {
      console.error('[Public API] Error fetching services:', error);
      
      // Return fallback data if database fails
      const fallbackServices = [
        {
          id: 'fallback-1',
          name: 'Face Painting',
          description: 'Professional face painting for all ages',
          duration: 180,
          price: 150,
          category: 'Face Painting',
          status: 'active',
          visible_on_public: true
        },
        {
          id: 'fallback-2', 
          name: 'Airbrush Body Art',
          description: 'Stunning airbrush body art and temporary tattoos',
          duration: 240,
          price: 200,
          category: 'Body Painting',
          status: 'active',
          visible_on_public: true
        },
        {
          id: 'fallback-3',
          name: 'Festival Braiding',
          description: 'Colorful braiding perfect for festivals and events',
          duration: 120,
          price: 80,
          category: 'Hair & Braiding',
          status: 'active',
          visible_on_public: true
        }
      ];

      console.log('[Public API] Using fallback services data');
      return res.status(200).json({
        services: fallbackServices,
        fallback: true,
        message: 'Using cached service data'
      });
    }

    // Filter and sanitize services for public display
    const publicServices = services.map(service => ({
      id: service.id,
      name: service.name,
      description: service.description,
      duration: service.duration,
      price: service.price,
      category: service.category,
      image_url: service.image_url,
      features: service.features,
      suitable_for: service.suitable_for
    }));

    // Set cache headers for performance
    res.setHeader('Cache-Control', 'public, s-maxage=300, stale-while-revalidate=600');
    
    console.log(`[Public API] Successfully fetched ${publicServices.length} services`);
    
    return res.status(200).json({
      services: publicServices,
      count: publicServices.length,
      fallback: false
    });

  } catch (error) {
    console.error('[Public API] Unexpected error fetching services:', error);
    
    // Return minimal fallback on critical error
    return res.status(500).json({
      error: 'Unable to fetch services',
      services: [],
      fallback: true
    });
  }
}
