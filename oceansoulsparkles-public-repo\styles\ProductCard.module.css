/* Product Card Styles */
.productCard {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f8f9fa;
  position: relative;
}

.productCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.productImageContainer {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
  background: #f8f9fa;
}

.productImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.productCard:hover .productImage {
  transform: scale(1.05);
}

.placeholderImage {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.placeholderIcon {
  font-size: 3rem;
  color: #adb5bd;
}

.stockBadge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stockBadge.inStock {
  background: #28a745;
}

.stockBadge.lowStock {
  background: #fd7e14;
}

.stockBadge.outOfStock {
  background: #dc3545;
}

.productOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.productCard:hover .productOverlay {
  opacity: 1;
}

.quickViewButton {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 18px;
}

.quickViewButton:hover {
  background: white;
  transform: scale(1.1);
}

.productContent {
  padding: 24px;
}

.productHeader {
  margin-bottom: 12px;
}

.productName {
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.3;
}

.productCategory {
  background: linear-gradient(135deg, #4ECDC4, #45b7aa);
  color: white;
  padding: 4px 10px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.productDescription {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 16px;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.productFeatures {
  margin-bottom: 16px;
}

.productFeatures ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.productFeatures li {
  color: #6c757d;
  font-size: 13px;
  margin-bottom: 4px;
  padding-left: 16px;
  position: relative;
}

.productFeatures li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

.productIngredients {
  margin-bottom: 16px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #4ECDC4;
}

.ingredientsLabel {
  font-weight: 600;
  color: #2c3e50;
  font-size: 12px;
  display: block;
  margin-bottom: 4px;
}

.ingredientsText {
  color: #6c757d;
  font-size: 12px;
}

.productFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.priceContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price {
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 700;
}

.originalPrice {
  color: #adb5bd;
  font-size: 1rem;
  text-decoration: line-through;
}

.productActions {
  flex: 1;
  margin-left: 16px;
}

.addToCartButton {
  width: 100%;
  padding: 10px 16px;
  background: linear-gradient(135deg, #4ECDC4 0%, #45b7aa 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.addToCartButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

.addToCartButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.outOfStockButton {
  width: 100%;
  padding: 10px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: not-allowed;
  opacity: 0.6;
}

.buttonSpinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.ecoBadge {
  position: absolute;
  bottom: 16px;
  left: 16px;
  background: rgba(40, 167, 69, 0.9);
  color: white;
  padding: 6px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  backdrop-filter: blur(10px);
}

.ecoIcon {
  font-size: 12px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .productCard {
    margin: 0 8px;
  }

  .productContent {
    padding: 20px;
  }

  .productName {
    font-size: 1.2rem;
  }

  .productFooter {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .priceContainer {
    justify-content: center;
  }

  .productActions {
    margin-left: 0;
  }

  .addToCartButton,
  .outOfStockButton {
    padding: 12px 16px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .productImageContainer {
    height: 200px;
  }

  .productContent {
    padding: 16px;
  }

  .productName {
    font-size: 1.1rem;
  }

  .price {
    font-size: 1.2rem;
  }
}
