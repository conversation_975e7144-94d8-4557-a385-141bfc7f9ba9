/* Dashboard Stats Component Styles */
.statsContainer {
  background: var(--admin-bg-primary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-xl);
  border: 1px solid var(--admin-border-light);
  box-shadow: 0 2px 4px var(--admin-shadow-light);
}

.sectionTitle {
  color: var(--admin-darker);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 var(--admin-spacing-lg) 0;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--admin-spacing-lg);
  margin-bottom: var(--admin-spacing-xl);
}

.statCard {
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-lg);
  border: 1px solid var(--admin-border-light);
  transition: all var(--admin-transition-normal);
  position: relative;
  overflow: hidden;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px var(--admin-shadow-medium);
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-dark));
}

.statHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-spacing-md);
}

.statIcon {
  font-size: 2rem;
  opacity: 0.8;
}

.statTrend {
  font-size: 0.85rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: var(--admin-radius-sm);
  background: rgba(255, 255, 255, 0.8);
}

.statBadge {
  background: var(--admin-warning);
  color: white;
  padding: 4px 8px;
  border-radius: var(--admin-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
}

.statIndicator {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-xs);
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--admin-success);
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--admin-success);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.statValue {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--admin-darker);
  line-height: 1;
  margin-bottom: var(--admin-spacing-xs);
}

.statLabel {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-gray);
  margin-bottom: var(--admin-spacing-xs);
}

.statSubtext {
  font-size: 0.85rem;
  color: var(--admin-gray);
  opacity: 0.8;
}

.breakdownSection {
  border-top: 1px solid var(--admin-border-light);
  padding-top: var(--admin-spacing-lg);
}

.breakdownTitle {
  color: var(--admin-darker);
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 var(--admin-spacing-md) 0;
}

.breakdownGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--admin-spacing-md);
}

.breakdownCard {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-md);
  padding: var(--admin-spacing-md);
  background: var(--admin-bg-primary);
  border-radius: var(--admin-radius-md);
  border: 1px solid var(--admin-border-light);
  transition: all var(--admin-transition-normal);
}

.breakdownCard:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px var(--admin-shadow-light);
}

.breakdownIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.breakdownContent {
  flex: 1;
}

.breakdownValue {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--admin-darker);
  line-height: 1;
  margin-bottom: 2px;
}

.breakdownLabel {
  font-size: 0.85rem;
  color: var(--admin-gray);
  font-weight: 500;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .statsContainer {
    padding: var(--admin-spacing-lg);
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: var(--admin-spacing-md);
  }

  .statCard {
    padding: var(--admin-spacing-md);
  }

  .statValue {
    font-size: 2rem;
  }

  .breakdownGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .statsContainer {
    padding: var(--admin-spacing-md);
  }

  .sectionTitle {
    font-size: 1.25rem;
  }

  .statHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-spacing-xs);
  }

  .statValue {
    font-size: 1.75rem;
  }

  .breakdownCard {
    padding: var(--admin-spacing-sm);
  }

  .breakdownIcon {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }

  .breakdownValue {
    font-size: 1.25rem;
  }
}
