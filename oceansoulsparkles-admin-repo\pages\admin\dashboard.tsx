import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import AdminLayout from '../../components/admin/AdminLayout';
import DashboardStats from '../../components/admin/DashboardStats';
import RecentBookings from '../../components/admin/RecentBookings';
import QuickActions from '../../components/admin/QuickActions';
import ActivityFeed from '../../components/admin/ActivityFeed';
import { useAuth } from '../../hooks/useAuth';
import styles from '../../styles/admin/Dashboard.module.css';

export default function AdminDashboard() {
  const router = useRouter();
  const { user, loading, error } = useAuth();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/admin/login');
      return;
    }

    if (user) {
      loadDashboardData();
    }
  }, [user, loading, router]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/admin/dashboard', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load dashboard data');
      }

      const data = await response.json();
      setDashboardData(data);
    } catch (error) {
      console.error('Error loading dashboard:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (loading || !user) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Admin Dashboard - Ocean Soul Sparkles</title>
        <meta name="description" content="Ocean Soul Sparkles admin dashboard" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <AdminLayout>
        <div className={styles.dashboard}>
          <div className={styles.header}>
            <div className={styles.welcomeSection}>
              <h1>Welcome back, {user.firstName}! ✨</h1>
              <p>Here&apos;s what&apos;s happening with Ocean Soul Sparkles today</p>
            </div>
            <div className={styles.headerActions}>
              <button 
                className={styles.refreshButton}
                onClick={loadDashboardData}
                disabled={isLoading}
              >
                🔄 Refresh
              </button>
            </div>
          </div>

          {isLoading ? (
            <div className={styles.loadingGrid}>
              <div className={styles.loadingCard}></div>
              <div className={styles.loadingCard}></div>
              <div className={styles.loadingCard}></div>
              <div className={styles.loadingCard}></div>
            </div>
          ) : (
            <div className={styles.dashboardGrid}>
              {/* Stats Overview */}
              <div className={styles.statsSection}>
                <DashboardStats data={dashboardData?.stats} />
              </div>

              {/* Quick Actions */}
              <div className={styles.quickActionsSection}>
                <QuickActions userRole={user.role} />
              </div>

              {/* Recent Bookings */}
              <div className={styles.bookingsSection}>
                <RecentBookings 
                  bookings={dashboardData?.recentBookings} 
                  userRole={user.role}
                />
              </div>

              {/* Activity Feed */}
              <div className={styles.activitySection}>
                <ActivityFeed 
                  activities={dashboardData?.recentActivity}
                  userRole={user.role}
                />
              </div>
            </div>
          )}

          {/* Role-specific sections */}
          {user.role === 'Artist' || user.role === 'Braider' ? (
            <div className={styles.artistSection}>
              <div className={styles.sectionHeader}>
                <h2>Your Performance</h2>
                <p>Track your bookings and earnings</p>
              </div>
              
              <div className={styles.performanceGrid}>
                <div className={styles.performanceCard}>
                  <h3>This Week</h3>
                  <div className={styles.metric}>
                    <span className={styles.value}>
                      {dashboardData?.artistStats?.weeklyBookings || 0}
                    </span>
                    <span className={styles.label}>Bookings</span>
                  </div>
                </div>
                
                <div className={styles.performanceCard}>
                  <h3>This Month</h3>
                  <div className={styles.metric}>
                    <span className={styles.value}>
                      ${dashboardData?.artistStats?.monthlyEarnings || 0}
                    </span>
                    <span className={styles.label}>Earnings</span>
                  </div>
                </div>
                
                <div className={styles.performanceCard}>
                  <h3>Rating</h3>
                  <div className={styles.metric}>
                    <span className={styles.value}>
                      {dashboardData?.artistStats?.averageRating || 'N/A'}
                    </span>
                    <span className={styles.label}>⭐ Average</span>
                  </div>
                </div>
              </div>
            </div>
          ) : null}

          {/* Admin-only sections */}
          {(user.role === 'DEV' || user.role === 'Admin') && (
            <div className={styles.adminSection}>
              <div className={styles.sectionHeader}>
                <h2>System Overview</h2>
                <p>Monitor system health and performance</p>
              </div>
              
              <div className={styles.systemGrid}>
                <div className={styles.systemCard}>
                  <h3>Database</h3>
                  <div className={styles.statusIndicator}>
                    <div className={`${styles.statusDot} ${styles.statusHealthy}`}></div>
                    <span>Healthy</span>
                  </div>
                </div>
                
                <div className={styles.systemCard}>
                  <h3>API Performance</h3>
                  <div className={styles.metric}>
                    <span className={styles.value}>
                      {dashboardData?.systemStats?.avgResponseTime || 'N/A'}ms
                    </span>
                    <span className={styles.label}>Avg Response</span>
                  </div>
                </div>
                
                <div className={styles.systemCard}>
                  <h3>Active Users</h3>
                  <div className={styles.metric}>
                    <span className={styles.value}>
                      {dashboardData?.systemStats?.activeUsers || 0}
                    </span>
                    <span className={styles.label}>Online Now</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </AdminLayout>
    </>
  );
}
