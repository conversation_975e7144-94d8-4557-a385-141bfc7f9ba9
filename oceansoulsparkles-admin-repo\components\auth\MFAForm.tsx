import { useState, useEffect, useRef } from 'react';
import styles from '../../styles/admin/MFAForm.module.css';

interface MFAFormProps {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  onSubmit: (mfaCode: string) => Promise<void>;
  onBack: () => void;
  isLoading: boolean;
}

export default function MFAForm({ user, onSubmit, onBack, isLoading }: MFAFormProps) {
  const [mfaCode, setMfaCode] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState('');
  const [timeLeft, setTimeLeft] = useState(30);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    // Focus first input on mount
    inputRefs.current[0]?.focus();

    // Start countdown timer
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleInputChange = (index: number, value: string) => {
    // Only allow digits
    if (!/^\d*$/.test(value)) return;

    const newCode = [...mfaCode];
    newCode[index] = value.slice(-1); // Only take the last character
    setMfaCode(newCode);
    setError('');

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when all fields are filled
    if (newCode.every(digit => digit !== '') && !isLoading) {
      handleSubmit(newCode.join(''));
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !mfaCode[index] && index > 0) {
      // Focus previous input on backspace
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowRight' && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6);
    
    if (pastedData.length === 6) {
      const newCode = pastedData.split('');
      setMfaCode(newCode);
      setError('');
      
      // Focus last input
      inputRefs.current[5]?.focus();
      
      // Auto-submit
      if (!isLoading) {
        handleSubmit(pastedData);
      }
    }
  };

  const handleSubmit = async (code: string) => {
    if (code.length !== 6) {
      setError('Please enter a complete 6-digit code');
      return;
    }

    try {
      await onSubmit(code);
    } catch (error) {
      setError('Invalid verification code. Please try again.');
      // Clear the form
      setMfaCode(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    }
  };

  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const code = mfaCode.join('');
    handleSubmit(code);
  };

  const handleResendCode = async () => {
    try {
      const response = await fetch('/api/auth/resend-mfa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });

      if (response.ok) {
        setTimeLeft(30);
        setError('');
        // Show success message or toast
      } else {
        setError('Failed to resend code. Please try again.');
      }
    } catch (error) {
      setError('Failed to resend code. Please try again.');
    }
  };

  return (
    <div className={styles.mfaForm}>
      <div className={styles.header}>
        <button
          type="button"
          className={styles.backButton}
          onClick={onBack}
          disabled={isLoading}
        >
          ← Back
        </button>
        <h2>Two-Factor Authentication</h2>
        <p>Enter the 6-digit code from your authenticator app</p>
      </div>

      <div className={styles.userInfo}>
        <div className={styles.avatar}>
          {user.firstName.charAt(0)}{user.lastName.charAt(0)}
        </div>
        <div className={styles.userDetails}>
          <div className={styles.userName}>
            {user.firstName} {user.lastName}
          </div>
          <div className={styles.userEmail}>{user.email}</div>
        </div>
      </div>

      {error && (
        <div className={styles.errorAlert}>
          <div className={styles.errorIcon}>⚠️</div>
          <div className={styles.errorMessage}>{error}</div>
        </div>
      )}

      <form onSubmit={handleManualSubmit} className={styles.form}>
        <div className={styles.codeInputContainer}>
          {mfaCode.map((digit, index) => (
            <input
              key={index}
              ref={(el) => {
                inputRefs.current[index] = el;
              }}
              type="text"
              inputMode="numeric"
              pattern="\d*"
              maxLength={1}
              value={digit}
              onChange={(e) => handleInputChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={handlePaste}
              className={`${styles.codeInput} ${error ? styles.inputError : ''}`}
              disabled={isLoading}
              autoComplete="one-time-code"
            />
          ))}
        </div>

        <button
          type="submit"
          className={styles.submitButton}
          disabled={isLoading || mfaCode.some(digit => digit === '')}
        >
          {isLoading ? (
            <div className={styles.loadingContainer}>
              <div className={styles.spinner}></div>
              <span>Verifying...</span>
            </div>
          ) : (
            <>
              <span>Verify Code</span>
              <div className={styles.buttonIcon}>✓</div>
            </>
          )}
        </button>
      </form>

      <div className={styles.footer}>
        <div className={styles.resendSection}>
          {timeLeft > 0 ? (
            <p className={styles.resendTimer}>
              Resend code in {timeLeft} seconds
            </p>
          ) : (
            <button
              type="button"
              className={styles.resendButton}
              onClick={handleResendCode}
              disabled={isLoading}
            >
              Resend Code
            </button>
          )}
        </div>

        <div className={styles.helpText}>
          <p>Having trouble? Contact your administrator for assistance.</p>
        </div>
      </div>
    </div>
  );
}
