import type { AppProps } from 'next/app';
import Head from 'next/head';
import { useEffect } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import '../styles/globals.css';

export default function AdminApp({ Component, pageProps }: AppProps) {
  useEffect(() => {
    // Security: Disable right-click context menu in production
    if (process.env.NODE_ENV === 'production') {
      const handleContextMenu = (e: MouseEvent) => {
        e.preventDefault();
        return false;
      };

      const handleKeyDown = (e: KeyboardEvent) => {
        // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
        if (
          e.key === 'F12' ||
          (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
          (e.ctrlKey && e.key === 'U')
        ) {
          e.preventDefault();
          return false;
        }
      };

      document.addEventListener('contextmenu', handleContextMenu);
      document.addEventListener('keydown', handleKeyDown);

      return () => {
        document.removeEventListener('contextmenu', handleContextMenu);
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, []);

  useEffect(() => {
    // Security: Clear console in production
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DEBUG_MODE !== 'true') {
      console.clear();
      console.log('%c🔒 Ocean Soul Sparkles Admin Portal', 'color: #3788d8; font-size: 20px; font-weight: bold;');
      console.log('%cThis is a secure admin portal. All access is monitored and logged.', 'color: #6c757d; font-size: 14px;');
      console.log('%cUnauthorized access is prohibited.', 'color: #dc3545; font-size: 14px; font-weight: bold;');
    }
  }, []);

  return (
    <>
      <Head>
        {/* Basic Meta Tags */}
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        
        {/* Security Meta Tags */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta name="referrer" content="strict-origin-when-cross-origin" />
        
        {/* Admin Portal Meta Tags */}
        <meta name="robots" content="noindex, nofollow, noarchive, nosnippet" />
        <meta name="googlebot" content="noindex, nofollow" />
        <meta name="description" content="Ocean Soul Sparkles Admin Portal - Secure staff access only" />
        
        {/* Favicon */}
        <link rel="icon" href="/admin/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/admin/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/admin/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/admin/favicon-16x16.png" />
        
        {/* Theme */}
        <meta name="theme-color" content="#3788d8" />
        <meta name="msapplication-TileColor" content="#3788d8" />
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href={process.env.NEXT_PUBLIC_SUPABASE_URL} />
        
        {/* DNS Prefetch */}
        <link rel="dns-prefetch" href="https://js.squareup.com" />
        <link rel="dns-prefetch" href="https://api.onesignal.com" />
        
        {/* Security: Content Security Policy */}
        <meta 
          httpEquiv="Content-Security-Policy" 
          content="default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.squareup.com https://cdn.onesignal.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.supabase.co https://js.squareup.com https://api.onesignal.com wss://*.supabase.co; frame-src 'self' https://js.squareup.com;" 
        />
        
        {/* Admin Portal Title */}
        <title>Ocean Soul Sparkles Admin Portal</title>
      </Head>

      {/* Main App Component */}
      <Component {...pageProps} />

      {/* Toast Notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        toastStyle={{
          fontFamily: 'inherit',
          fontSize: '14px'
        }}
      />

      {/* Security Watermark (Production Only) */}
      {process.env.NODE_ENV === 'production' && (
        <div
          style={{
            position: 'fixed',
            bottom: '10px',
            right: '10px',
            background: 'rgba(0, 0, 0, 0.1)',
            color: 'rgba(0, 0, 0, 0.3)',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '10px',
            fontWeight: 'bold',
            pointerEvents: 'none',
            zIndex: 9999,
            userSelect: 'none'
          }}
        >
          ADMIN PORTAL
        </div>
      )}

      {/* Development Mode Indicator */}
      {process.env.NODE_ENV === 'development' && (
        <div
          style={{
            position: 'fixed',
            top: '0',
            left: '0',
            right: '0',
            background: '#ff6b6b',
            color: 'white',
            padding: '4px',
            textAlign: 'center',
            fontSize: '12px',
            fontWeight: 'bold',
            zIndex: 10000
          }}
        >
          🚧 DEVELOPMENT MODE - ADMIN PORTAL 🚧
        </div>
      )}
    </>
  );
}
