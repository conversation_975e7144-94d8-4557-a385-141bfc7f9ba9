/* Admin Sidebar Styles */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background: var(--admin-bg-primary);
  border-right: 1px solid var(--admin-border-light);
  display: flex;
  flex-direction: column;
  transition: width var(--admin-transition-normal);
  z-index: var(--admin-z-fixed);
  overflow: hidden;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar.mobile {
  transform: translateX(-100%);
  transition: transform var(--admin-transition-normal);
}

.sidebar.mobile:not(.collapsed) {
  transform: translateX(0);
}

.sidebarHeader {
  padding: var(--admin-spacing-lg);
  border-bottom: 1px solid var(--admin-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-md);
}

.logoIcon {
  font-size: 2rem;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logoIconOnly {
  font-size: 1.8rem;
  text-align: center;
  width: 100%;
}

.logoText {
  display: flex;
  flex-direction: column;
}

.logoTitle {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--admin-darker);
  line-height: 1;
}

.logoSubtitle {
  font-size: 0.8rem;
  color: var(--admin-gray);
  font-weight: 500;
}

.toggleButton {
  background: var(--admin-bg-tertiary);
  border: none;
  border-radius: var(--admin-radius-sm);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  color: var(--admin-gray);
}

.toggleButton:hover {
  background: var(--admin-primary);
  color: white;
}

.userInfo {
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  border-bottom: 1px solid var(--admin-border-light);
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-md);
}

.userAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-dark));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.userDetails {
  flex: 1;
  min-width: 0;
}

.userName {
  font-weight: 600;
  color: var(--admin-darker);
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userRole {
  font-size: 0.75rem;
  color: var(--admin-gray);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.navigation {
  flex: 1;
  overflow-y: auto;
  padding: var(--admin-spacing-md) 0;
}

.menuList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menuItem {
  margin-bottom: 2px;
}

.menuLink {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-md);
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  color: var(--admin-gray);
  text-decoration: none;
  transition: all var(--admin-transition-normal);
  position: relative;
}

.menuLink:hover {
  background: var(--admin-bg-secondary);
  color: var(--admin-darker);
}

.menuLink.active {
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-dark));
  color: white;
}

.menuLink.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--admin-accent);
}

.menuIcon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.menuLabel {
  font-weight: 500;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expandButton {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 2px;
  margin-left: auto;
  font-size: 0.7rem;
  transition: transform var(--admin-transition-normal);
}

.submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  background: var(--admin-bg-secondary);
}

.submenuItem {
  margin: 0;
}

.submenuLink {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
  padding: var(--admin-spacing-sm) var(--admin-spacing-lg) var(--admin-spacing-sm) 60px;
  color: var(--admin-gray);
  text-decoration: none;
  transition: all var(--admin-transition-normal);
  font-size: 0.85rem;
}

.submenuLink:hover {
  background: var(--admin-bg-tertiary);
  color: var(--admin-darker);
}

.submenuLink.active {
  background: var(--admin-primary);
  color: white;
}

.submenuIcon {
  font-size: 1rem;
  width: 16px;
  text-align: center;
}

.submenuLabel {
  font-weight: 500;
}

.sidebarFooter {
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  border-top: 1px solid var(--admin-border-light);
  margin-top: auto;
}

.footerContent {
  margin-bottom: var(--admin-spacing-md);
}

.versionInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-spacing-sm);
}

.version {
  font-size: 0.75rem;
  color: var(--admin-gray);
  font-weight: 500;
}

.environment {
  background: var(--admin-success);
  color: white;
  padding: 2px 6px;
  border-radius: var(--admin-radius-sm);
  font-size: 0.7rem;
  font-weight: 600;
}

.securityIndicator {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
  padding: var(--admin-spacing-sm);
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-md);
  border: 1px solid var(--admin-border-light);
}

.securityIcon {
  font-size: 1rem;
  color: var(--admin-success);
}

.securityText {
  font-size: 0.75rem;
  color: var(--admin-gray);
  font-weight: 500;
}

/* Collapsed State Adjustments */
.sidebar.collapsed .userInfo {
  justify-content: center;
}

.sidebar.collapsed .userDetails {
  display: none;
}

.sidebar.collapsed .menuLink {
  justify-content: center;
  padding: var(--admin-spacing-md);
}

.sidebar.collapsed .menuLabel,
.sidebar.collapsed .expandButton {
  display: none;
}

.sidebar.collapsed .footerContent {
  display: none;
}

.sidebar.collapsed .securityIndicator {
  justify-content: center;
}

.sidebar.collapsed .securityText {
  display: none;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 280px;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  }

  .sidebar.collapsed {
    width: 280px;
  }

  .toggleButton {
    display: none;
  }
}

/* Scrollbar Styling */
.navigation::-webkit-scrollbar {
  width: 4px;
}

.navigation::-webkit-scrollbar-track {
  background: transparent;
}

.navigation::-webkit-scrollbar-thumb {
  background: var(--admin-border-medium);
  border-radius: 2px;
}

.navigation::-webkit-scrollbar-thumb:hover {
  background: var(--admin-gray);
}
