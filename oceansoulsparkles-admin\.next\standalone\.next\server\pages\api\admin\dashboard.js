"use strict";(()=>{var e={};e.id=438,e.ids=[438],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},195:(e,t,a)=>{a.r(t),a.d(t,{config:()=>g,default:()=>u,routeModule:()=>m});var o={};a.r(o),a.d(o,{default:()=>d});var n=a(1802),r=a(7153),s=a(8781),i=a(7474);let l=(0,a(2885).createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function d(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let a=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!a)return t.status(401).json({error:"No authentication token"});let o=await (0,i.Wg)(a);if(!o.valid||!o.user)return t.status(401).json({error:"Invalid authentication"});let n=o.user,r=await c(n.role,n.id);return t.status(200).json(r)}catch(e){return console.error("Dashboard API error:",e),t.status(500).json({error:"Internal server error"})}}async function c(e,t){let a=new Date,o=new Date(a.getFullYear(),a.getMonth(),1),n=new Date(a.getFullYear(),a.getMonth()-1,1),r=new Date(a.getFullYear(),a.getMonth(),0);try{let a={totalBookings:0,totalRevenue:0,activeCustomers:0,pendingBookings:0,completedBookings:0,cancelledBookings:0,averageBookingValue:0,monthlyGrowth:0},s=[],i=[],d=null,c=null;if("DEV"===e||"Admin"===e){let{data:e}=await l.from("bookings").select("*").gte("created_at",o.toISOString());e&&(a.totalBookings=e.length,a.totalRevenue=e.filter(e=>"completed"===e.status).reduce((e,t)=>e+(t.total_amount||0),0),a.pendingBookings=e.filter(e=>"pending"===e.status).length,a.completedBookings=e.filter(e=>"completed"===e.status).length,a.cancelledBookings=e.filter(e=>"cancelled"===e.status).length,a.averageBookingValue=a.totalBookings>0?a.totalRevenue/a.completedBookings:0);let{count:t}=await l.from("customers").select("*",{count:"exact",head:!0}).gte("last_activity",new Date(Date.now()-2592e6).toISOString());a.activeCustomers=t||0;let{data:n}=await l.from("bookings").select(`
          *,
          customers (first_name, last_name),
          services (name),
          artists (first_name, last_name)
        `).order("created_at",{ascending:!1}).limit(10);s=n||[],c={avgResponseTime:150,activeUsers:5,systemHealth:"healthy"}}else if("Artist"===e||"Braider"===e){let{data:e}=await l.from("bookings").select("*").eq("artist_id",t).gte("created_at",o.toISOString());e&&(a.totalBookings=e.length,a.totalRevenue=e.filter(e=>"completed"===e.status).reduce((e,t)=>e+(t.total_amount||0),0),a.pendingBookings=e.filter(e=>"pending"===e.status).length,a.completedBookings=e.filter(e=>"completed"===e.status).length,a.cancelledBookings=e.filter(e=>"cancelled"===e.status).length);let{data:n}=await l.from("bookings").select("*").eq("artist_id",t).gte("created_at",new Date(Date.now()-6048e5).toISOString()),{data:r}=await l.from("bookings").select("*").eq("artist_id",t).gte("created_at",o.toISOString()).eq("status","completed");d={weeklyBookings:n?.length||0,monthlyEarnings:r?.reduce((e,t)=>e+(t.total_amount||0),0)||0,averageRating:4.8};let{data:i}=await l.from("bookings").select(`
          *,
          customers (first_name, last_name),
          services (name)
        `).eq("artist_id",t).order("created_at",{ascending:!1}).limit(10);s=i||[]}let{data:u}=await l.from("bookings").select("total_amount").gte("created_at",n.toISOString()).lte("created_at",r.toISOString()).eq("status","completed"),g=u?.reduce((e,t)=>e+(t.total_amount||0),0)||0;return g>0&&(a.monthlyGrowth=(a.totalRevenue-g)/g*100),i=[{id:"1",type:"booking",title:"New booking created",description:"Sarah Johnson booked Festival Face Paint",timestamp:new Date(Date.now()-18e5).toISOString(),user:"Emma Wilson"},{id:"2",type:"payment",title:"Payment received",description:"Payment of $120 received",timestamp:new Date(Date.now()-27e5).toISOString(),user:"System"},{id:"3",type:"customer",title:"New customer registered",description:"Mike Chen created account",timestamp:new Date(Date.now()-36e5).toISOString(),user:"Mike Chen"}],{stats:a,recentBookings:s,recentActivity:i,artistStats:d,systemStats:c}}catch(e){return console.error("Error fetching dashboard data:",e),{stats:{totalBookings:0,totalRevenue:0,activeCustomers:0,pendingBookings:0,completedBookings:0,cancelledBookings:0,averageBookingValue:0,monthlyGrowth:0},recentBookings:[],recentActivity:[],artistStats:null,systemStats:null}}}let u=(0,s.l)(o,"default"),g=(0,s.l)(o,"config"),m=new n.PagesAPIRouteModule({definition:{kind:r.x.PAGES_API,page:"/api/admin/dashboard",pathname:"/api/admin/dashboard",bundlePath:"",filename:""},userland:o})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[805],()=>a(195));module.exports=o})();