import { useState, useEffect } from 'react';
import Head from 'next/head';
import Layout from '@/components/Layout';
import BookingForm from '@/components/BookingForm';
import ServiceSelector from '@/components/ServiceSelector';
import { publicData } from '@/lib/supabase';
import { useCustomer } from '@/contexts/CustomerContext';
import { toast } from 'react-toastify';
import styles from '@/styles/Booking.module.css';

export default function BookOnline() {
  const [services, setServices] = useState([]);
  const [selectedService, setSelectedService] = useState(null);
  const [loading, setLoading] = useState(true);
  const [step, setStep] = useState(1); // 1: Select Service, 2: Book Service
  
  const { customer, isAuthenticated } = useCustomer();

  // Fetch services on component mount
  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const { data, error } = await publicData.getServices();
      
      if (error) {
        console.error('Error fetching services:', error);
        toast.error('Unable to load services. Please try again.');
        return;
      }

      setServices(data || []);
    } catch (error) {
      console.error('Error fetching services:', error);
      toast.error('Unable to load services. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleServiceSelect = (service) => {
    setSelectedService(service);
    setStep(2);
  };

  const handleBackToServices = () => {
    setSelectedService(null);
    setStep(1);
  };

  const handleBookingComplete = () => {
    // Reset form and show success
    setSelectedService(null);
    setStep(1);
    toast.success('Booking request submitted successfully! We will contact you to confirm your appointment.');
  };

  return (
    <Layout>
      <Head>
        <title>Book Online - Ocean Soul Sparkles</title>
        <meta name="description" content="Book your face painting, body art, or braiding service online with Ocean Soul Sparkles. Easy online booking for events, parties, and individual sessions." />
        <meta name="keywords" content="book online, face painting booking, body art appointment, braiding service, Melbourne events" />
      </Head>

      <div className={styles.bookingContainer}>
        <div className={styles.bookingHeader}>
          <h1>Book Your Sparkle Experience</h1>
          <p>Choose from our range of magical services and book your appointment online</p>
          
          {/* Progress Indicator */}
          <div className={styles.progressIndicator}>
            <div className={`${styles.progressStep} ${step >= 1 ? styles.active : ''}`}>
              <span className={styles.stepNumber}>1</span>
              <span className={styles.stepLabel}>Select Service</span>
            </div>
            <div className={styles.progressLine}></div>
            <div className={`${styles.progressStep} ${step >= 2 ? styles.active : ''}`}>
              <span className={styles.stepNumber}>2</span>
              <span className={styles.stepLabel}>Book Appointment</span>
            </div>
          </div>
        </div>

        <div className={styles.bookingContent}>
          {step === 1 && (
            <div className={styles.serviceSelection}>
              <div className={styles.sectionHeader}>
                <h2>Choose Your Service</h2>
                <p>Select the service you'd like to book from our available options</p>
              </div>

              {loading ? (
                <div className={styles.loadingContainer}>
                  <div className={styles.loadingSpinner}></div>
                  <p>Loading services...</p>
                </div>
              ) : (
                <ServiceSelector
                  services={services}
                  onServiceSelect={handleServiceSelect}
                  loading={loading}
                />
              )}

              {!loading && services.length === 0 && (
                <div className={styles.noServices}>
                  <h3>No Services Available</h3>
                  <p>We're currently updating our services. Please check back soon or contact us directly.</p>
                  <a href="/contact" className="button">Contact Us</a>
                </div>
              )}
            </div>
          )}

          {step === 2 && selectedService && (
            <div className={styles.bookingFormSection}>
              <div className={styles.sectionHeader}>
                <button 
                  onClick={handleBackToServices}
                  className={styles.backButton}
                >
                  ← Back to Services
                </button>
                <h2>Book: {selectedService.name}</h2>
                <p>Fill in your details to request this service</p>
              </div>

              <div className={styles.selectedServiceSummary}>
                <div className={styles.serviceSummaryCard}>
                  <h3>{selectedService.name}</h3>
                  <p className={styles.serviceDescription}>{selectedService.description}</p>
                  <div className={styles.serviceDetails}>
                    <span className={styles.serviceDuration}>
                      ⏱️ {Math.floor(selectedService.duration / 60)}h {selectedService.duration % 60}m
                    </span>
                    <span className={styles.servicePrice}>
                      💰 From ${selectedService.price}
                    </span>
                  </div>
                </div>
              </div>

              <BookingForm
                service={selectedService}
                customer={customer}
                isAuthenticated={isAuthenticated}
                onBookingComplete={handleBookingComplete}
                onCancel={handleBackToServices}
              />
            </div>
          )}
        </div>

        {/* Customer Account Prompt */}
        {!isAuthenticated && (
          <div className={styles.accountPrompt}>
            <div className={styles.promptCard}>
              <h3>Want to track your bookings?</h3>
              <p>Create an account to view your booking history and manage appointments</p>
              <div className={styles.promptActions}>
                <a href="/signup" className="button button--outline">Create Account</a>
                <a href="/login" className="button button--text">Sign In</a>
              </div>
            </div>
          </div>
        )}

        {/* Booking Information */}
        <div className={styles.bookingInfo}>
          <div className={styles.infoGrid}>
            <div className={styles.infoCard}>
              <h3>📅 Flexible Scheduling</h3>
              <p>We offer flexible appointment times to fit your schedule. Weekend and evening appointments available.</p>
            </div>
            <div className={styles.infoCard}>
              <h3>🎨 Professional Artists</h3>
              <p>All our artists are experienced professionals who use high-quality, skin-safe products.</p>
            </div>
            <div className={styles.infoCard}>
              <h3>🌿 Eco-Friendly</h3>
              <p>We use biodegradable glitter and eco-friendly products that are safe for you and the environment.</p>
            </div>
            <div className={styles.infoCard}>
              <h3>💳 Secure Payments</h3>
              <p>Pay securely online or in person. We accept all major credit cards and digital payments.</p>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className={styles.contactSection}>
          <h3>Need Help with Your Booking?</h3>
          <p>Our team is here to help you create the perfect sparkle experience</p>
          <div className={styles.contactOptions}>
            <a href="mailto:<EMAIL>" className={styles.contactOption}>
              📧 <EMAIL>
            </a>
            <a href="/contact" className={styles.contactOption}>
              💬 Contact Form
            </a>
          </div>
        </div>
      </div>
    </Layout>
  );
}
