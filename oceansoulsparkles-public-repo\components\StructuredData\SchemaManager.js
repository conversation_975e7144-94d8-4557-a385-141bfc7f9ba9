import Head from 'next/head';
import { useRouter } from 'next/router';

export default function SchemaManager() {
  const router = useRouter();

  // Base organization schema
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Ocean Soul Sparkles",
    "description": "Professional face painting, airbrush body art, and braiding services in Melbourne. Eco-friendly and sustainable glitter options available.",
    "url": "https://www.oceansoulsparkles.com.au",
    "telephone": "+61-XXX-XXX-XXX", // Replace with actual phone
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Melbourne",
      "addressRegion": "Victoria",
      "addressCountry": "AU"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": -37.8136,
      "longitude": 144.9631
    },
    "openingHours": [
      "Mo-Fr 09:00-17:00",
      "Sa 09:00-15:00"
    ],
    "priceRange": "$$",
    "paymentAccepted": ["Cash", "Credit Card", "Square"],
    "currenciesAccepted": "AUD",
    "serviceArea": {
      "@type": "GeoCircle",
      "geoMidpoint": {
        "@type": "GeoCoordinates",
        "latitude": -37.8136,
        "longitude": 144.9631
      },
      "geoRadius": "50000"
    },
    "sameAs": [
      "https://www.facebook.com/OceanSoulSparkles/",
      "https://www.instagram.com/oceansoulsparkles"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Ocean Soul Sparkles Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Face Painting",
            "description": "Professional face painting for all ages and events"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Airbrush Body Art",
            "description": "Stunning airbrush body art and temporary tattoos"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Festival Braiding",
            "description": "Colorful braiding perfect for festivals and events"
          }
        }
      ]
    }
  };

  // Page-specific schemas
  const getPageSchema = () => {
    const { pathname } = router;

    switch (pathname) {
      case '/':
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "Ocean Soul Sparkles",
          "url": "https://www.oceansoulsparkles.com.au",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "https://www.oceansoulsparkles.com.au/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
          }
        };

      case '/services':
        return {
          "@context": "https://schema.org",
          "@type": "ItemList",
          "name": "Ocean Soul Sparkles Services",
          "description": "Professional face painting, body art, and braiding services",
          "numberOfItems": 3,
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "item": {
                "@type": "Service",
                "name": "Face Painting",
                "description": "Professional face painting for all ages",
                "provider": {
                  "@type": "LocalBusiness",
                  "name": "Ocean Soul Sparkles"
                }
              }
            },
            {
              "@type": "ListItem",
              "position": 2,
              "item": {
                "@type": "Service",
                "name": "Airbrush Body Art",
                "description": "Stunning airbrush body art and temporary tattoos",
                "provider": {
                  "@type": "LocalBusiness",
                  "name": "Ocean Soul Sparkles"
                }
              }
            },
            {
              "@type": "ListItem",
              "position": 3,
              "item": {
                "@type": "Service",
                "name": "Festival Braiding",
                "description": "Colorful braiding perfect for festivals and events",
                "provider": {
                  "@type": "LocalBusiness",
                  "name": "Ocean Soul Sparkles"
                }
              }
            }
          ]
        };

      case '/shop':
        return {
          "@context": "https://schema.org",
          "@type": "Store",
          "name": "Ocean Soul Sparkles Shop",
          "description": "Eco-friendly glitter, face paints, and beauty products",
          "url": "https://www.oceansoulsparkles.com.au/shop",
          "parentOrganization": {
            "@type": "LocalBusiness",
            "name": "Ocean Soul Sparkles"
          }
        };

      case '/book-online':
        return {
          "@context": "https://schema.org",
          "@type": "ReservationAction",
          "name": "Book Ocean Soul Sparkles Service",
          "description": "Book face painting, body art, or braiding services online",
          "target": "https://www.oceansoulsparkles.com.au/book-online",
          "provider": {
            "@type": "LocalBusiness",
            "name": "Ocean Soul Sparkles"
          }
        };

      default:
        return null;
    }
  };

  const pageSchema = getPageSchema();

  return (
    <Head>
      {/* Organization Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      
      {/* Page-specific Schema */}
      {pageSchema && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(pageSchema)
          }}
        />
      )}
    </Head>
  );
}
