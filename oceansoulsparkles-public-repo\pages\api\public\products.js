import { publicData } from '@/lib/supabase';

/**
 * Public API endpoint for fetching products
 * This endpoint provides products data for the public shop
 * No authentication required - only returns active products
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { category, featured } = req.query;

  try {
    console.log('[Public API] Fetching products for public shop');

    // Fetch active products using public data access
    const { data: products, error } = await publicData.getProducts(category);

    if (error) {
      console.error('[Public API] Error fetching products:', error);
      
      // Return fallback data if database fails
      const fallbackProducts = [
        {
          id: 'fallback-1',
          name: 'Biodegradable Glitter Set',
          description: 'Eco-friendly glitter made from eucalyptus trees',
          price: 25.00,
          category_name: 'Glitter',
          status: 'active',
          image_url: '/images/products/biodegradable-glitter.jpg',
          stock_quantity: 50
        },
        {
          id: 'fallback-2',
          name: 'Face Paint Kit',
          description: 'Professional face paint kit with brushes',
          price: 45.00,
          category_name: 'Face Paint',
          status: 'active',
          image_url: '/images/products/face-paint-kit.jpg',
          stock_quantity: 25
        },
        {
          id: 'fallback-3',
          name: 'Hair Chalk Set',
          description: 'Temporary hair color chalk for festivals',
          price: 15.00,
          category_name: 'Hair',
          status: 'active',
          image_url: '/images/products/hair-chalk.jpg',
          stock_quantity: 30
        }
      ];

      console.log('[Public API] Using fallback products data');
      return res.status(200).json({
        products: fallbackProducts,
        fallback: true,
        message: 'Using cached product data'
      });
    }

    // Filter and sanitize products for public display
    const publicProducts = products
      .filter(product => product.stock_quantity > 0) // Only show in-stock items
      .map(product => ({
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        category_name: product.category_name,
        image_url: product.image_url,
        stock_quantity: product.stock_quantity,
        features: product.features,
        ingredients: product.ingredients
      }));

    // Apply featured filter if requested
    if (featured === 'true') {
      // For now, return first 6 products as featured
      publicProducts.splice(6);
    }

    // Set cache headers for performance
    res.setHeader('Cache-Control', 'public, s-maxage=600, stale-while-revalidate=1200');
    
    console.log(`[Public API] Successfully fetched ${publicProducts.length} products`);
    
    return res.status(200).json({
      products: publicProducts,
      count: publicProducts.length,
      category: category || 'all',
      fallback: false
    });

  } catch (error) {
    console.error('[Public API] Unexpected error fetching products:', error);
    
    // Return minimal fallback on critical error
    return res.status(500).json({
      error: 'Unable to fetch products',
      products: [],
      fallback: true
    });
  }
}
