import Head from 'next/head'
import Link from 'next/link'
import { useEffect, useRef } from 'react'
import styles from '@/styles/Home.module.css'
import Layout from '@/components/Layout'
import HeroSection from '@/components/HeroSection'
import AnimatedSection from '@/components/AnimatedSection'
import StaggeredList from '@/components/StaggeredList'
import OceanSparklesShowcase from '@/components/OceanSparklesShowcase'
import PageSEO from '@/components/SEO/PageSEO'

export default function Home() {
  // Ref for scroll to services functionality
  const servicesRef = useRef(null);

  const scrollToServices = () => {
    servicesRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <Layout>
      <Head>
        <title>Ocean Soul Sparkles - Face Painting & Body Art Services</title>
        <meta name="description" content="Professional face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne. Eco-friendly and sustainable glitter options available." />
        <meta name="keywords" content="face painting, body art, airbrush, braiding, melbourne, events, festivals, eco-friendly glitter" />
        <meta name="google-site-verification" content="HtjqFmAXzFBvlS4IEWJe8iyN_UK4dROGIIt_oY-i1Ag" />
      </Head>

      <PageSEO
        title="OceanSoulSparkles | Melbourne Facepaint & Entertainment"
        description="OceanSoulSparkles offers face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne. Eco-friendly and sustainable glitter options available."
        ogImage="https://www.oceansoulsparkles.com.au/images/gallery/gallery-1.jpg"
      />

      <main className={styles.main}>
        <HeroSection
          title="A world of sparkling self expression awaits you."
          subtitle="We exist to help you unleash your creativity and SHINE. Let's create something wild & magical together."
          backgroundImage="/UV-Generic-Psychadelia.jpg"
          ctaText="Sparkle Up"
          ctaLink="/book-online"
          secondaryCtaText="Our Services"
          secondaryCtaLink="#services"
          height="100vh"
        />

        <OceanSparklesShowcase
          title="Dive Into a World of Magical Artistry"
          subtitle="Express yourself with vibrant colors, dazzling sparkles, and creative designs that capture your unique spirit"
          ctaText="Let's Create Magic"
          ctaLink="/book-online"
        />

        <section id="services" ref={servicesRef} className={styles.services}>
          <AnimatedSection animation="fade-in">
            <h2 className={styles.sectionTitle}>Our Services</h2>
            <p className={styles.sectionDescription}>We offer a range of services to suit all needs - hit the link to see more!</p>
          </AnimatedSection>

          <StaggeredList className={styles.serviceGrid} staggerDelay={150}>
            <div className={styles.serviceCard}>
              <div className={styles.serviceImage}>
                <img src="/Airbrush Face Body Painting.png" alt="Airbrush Face & Body Painting" className={styles.serviceImg} />
              </div>
              <h3>Airbrush Face & Body Painting</h3>
              <p>
                Add flair and colour to any event with our airbrush face and body painting.
                From intricate designs to bold statements, we tailor our artistry to suit your theme or outfit.
                Perfect for festivals, birthdays, and corporate gatherings.
              </p>
              <Link href="/services" className={styles.serviceLink}>Learn More</Link>
            </div>

            <div className={styles.serviceCard}>
              <div className={styles.serviceImage}>
                <img src="/images/services/festival-braids.jpg" alt="Braiding" className={styles.serviceImg} />
              </div>
              <h3>Braiding</h3>
              <p>
                Transform your look with our vibrant braiding services, including festival-ready styles
                and coloured extensions to match your vibe. We offer single bookings, braid parties,
                and festival services, starting from $60.
              </p>
              <Link href="/services" className={styles.serviceLink}>Learn More</Link>
            </div>

            <div className={styles.serviceCard}>
              <div className={styles.serviceImage}>
                <img src="/Airbrush Temporary Tattoos.png" alt="Airbrush Temporary Tattoos" className={styles.serviceImg} />
              </div>
              <h3>Airbrush Temporary Tattoos</h3>
              <p>
                Explore our stunning airbrush temporary tattoos that last for days! Perfect for events,
                parties, or just trying out a new look without the commitment. Our designs range from
                delicate and intricate to bold and eye-catching.
              </p>
              <Link href="/services" className={styles.serviceLink}>Learn More</Link>
            </div>
          </StaggeredList>

          <AnimatedSection animation="fade-in" delay={300}>
            <div className={styles.servicesLink}>
              <Link href="/services" className="button">
                Explore All Services
              </Link>
            </div>
          </AnimatedSection>
        </section>

        <section className={styles.ecoFriendly}>
          <AnimatedSection animation="slide-right" className={styles.ecoContent}>
            <h2 className={styles.ecoTitle}>Shine Sustainably With Eco-Friendly Glitter</h2>
            <p className={styles.ecoDescription}>
              We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do,
              and that's why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!
            </p>
            <p className={styles.ecoDescription}>
              Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water,
              ensuring it leaves no harmful trace. We're proud to be as friendly to the planet as we are to our customers,
              and to provide a way for both you and the earth to shine!
            </p>
            <Link href="/shop" className="button button--secondary mt-4">
              Shop Eco Glitter
            </Link>
          </AnimatedSection>
          <AnimatedSection animation="slide-left" className={styles.ecoImage}>
            <img src="/images/products/biodegradable-glitter.jpg" alt="Eco-Friendly Biodegradable Glitter" className={styles.ecoImg} />
          </AnimatedSection>
        </section>

        <section className={styles.gallery}>
          <AnimatedSection animation="fade-in">
            <h2 className={styles.sectionTitle}>Our Gallery</h2>
            <p className={styles.sectionDescription}>Take a peek at some of our magical creations</p>
          </AnimatedSection>

          <StaggeredList className={styles.galleryGrid} staggerDelay={100} baseDelay={200}>
            <div className={styles.galleryItem}>
              <img src="/images/gallery/gallery-1.jpg" alt="Ocean Soul Sparkles Gallery" className={styles.galleryImg} />
              <div className={styles.galleryOverlay}>
                <span>Face Painting</span>
              </div>
            </div>
            <div className={styles.galleryItem}>
              <img src="/images/gallery/gallery-2.jpg" alt="Ocean Soul Sparkles Gallery" className={styles.galleryImg} />
              <div className={styles.galleryOverlay}>
                <span>Festival Makeup</span>
              </div>
            </div>
            <div className={styles.galleryItem}>
              <img src="/images/gallery/gallery-3.jpg" alt="Ocean Soul Sparkles Gallery" className={styles.galleryImg} />
              <div className={styles.galleryOverlay}>
                <span>Airbrush Art</span>
              </div>
            </div>
            <div className={styles.galleryItem}>
              <img src="/images/gallery/gallery-4.jpg" alt="Ocean Soul Sparkles Gallery" className={styles.galleryImg} />
              <div className={styles.galleryOverlay}>
                <span>Kids Designs</span>
              </div>
            </div>
            <div className={styles.galleryItem}>
              <img src="/images/gallery/gallery-5.jpg" alt="Ocean Soul Sparkles Gallery" className={styles.galleryImg} />
              <div className={styles.galleryOverlay}>
                <span>Braiding</span>
              </div>
            </div>
            <div className={styles.galleryItem}>
              <img src="/images/gallery/gallery-6.jpg" alt="Ocean Soul Sparkles Gallery" className={styles.galleryImg} />
              <div className={styles.galleryOverlay}>
                <span>Glitter Art</span>
              </div>
            </div>
            <div className={styles.galleryItem}>
              <img src="/images/gallery/gallery-7.jpg" alt="Ocean Soul Sparkles Gallery" className={styles.galleryImg} />
              <div className={styles.galleryOverlay}>
                <span>Special Events</span>
              </div>
            </div>
            <div className={styles.galleryItem}>
              <img src="/images/products/biodegradable-glitter.jpg" alt="Ocean Soul Sparkles Gallery" className={styles.galleryImg} />
              <div className={styles.galleryOverlay}>
                <span>Eco Products</span>
              </div>
            </div>
          </StaggeredList>

          <AnimatedSection animation="fade-in" delay={500}>
            <div className={styles.galleryLink}>
              <Link href="/gallery" className="button">
                View Full Gallery
              </Link>
              <Link href="/book-online" className="button button--outline ml-4">
                Book Your Experience
              </Link>
            </div>
          </AnimatedSection>
        </section>

        <section className={styles.contact} id="contact">
          <AnimatedSection animation="fade-in">
            <h2 className={styles.sectionTitle}>Get In Touch</h2>
            <p className={styles.sectionDescription}>Have questions or ready to book? Send us a message!</p>
          </AnimatedSection>

          <div className={styles.contactContainer}>
            <AnimatedSection animation="slide-right" className={styles.contactInfo}>
              <div className={styles.contactCard}>
                <h3>Contact Information</h3>
                <p>We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help.</p>

                <div className={styles.contactDetail}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>

                <div className={styles.contactDetail}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                  </svg>
                  <span>Melbourne, Victoria</span>
                </div>

                <div className={styles.contactSocial}>
                  <a href="https://www.instagram.com/oceansoulsparkles" target="_blank" rel="noopener noreferrer">
                    <img src="/images/social/instagram-icon.png" alt="Instagram" />
                  </a>
                  <a href="https://www.facebook.com/OceanSoulSparkles/" target="_blank" rel="noopener noreferrer">
                    <img src="/images/social/facebook-icon.png" alt="Facebook" />
                  </a>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection animation="slide-left" className={styles.contactFormContainer}>
              <form className={styles.contactForm}>
                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="name">Name</label>
                    <input id="name" type="text" placeholder="Your Name" className={styles.formInput} />
                  </div>
                  <div className={styles.formGroup}>
                    <label htmlFor="email">Email</label>
                    <input id="email" type="email" placeholder="Your Email" className={styles.formInput} />
                  </div>
                </div>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="subject">Subject</label>
                    <input id="subject" type="text" placeholder="Subject" className={styles.formInput} />
                  </div>
                  <div className={styles.formGroup}>
                    <label htmlFor="phone">Phone</label>
                    <input id="phone" type="tel" placeholder="Your Phone" className={styles.formInput} />
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="message">Message</label>
                  <textarea id="message" placeholder="Your Message" className={styles.formTextarea}></textarea>
                </div>

                <button type="submit" className="button">Send Message</button>
              </form>
            </AnimatedSection>
          </div>
        </section>
      </main>
    </Layout>
  )
}
