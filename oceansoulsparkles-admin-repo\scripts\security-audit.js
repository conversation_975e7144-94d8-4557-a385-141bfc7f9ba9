#!/usr/bin/env node

/**
 * Ocean Soul Sparkles Admin Subdomain - Security Audit Script
 * Comprehensive security validation for production deployment
 */

const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

console.log('🔒 Ocean Soul Sparkles Admin Subdomain - Security Audit\n');

let totalChecks = 0;
let passedChecks = 0;
let warnings = 0;
let criticalIssues = 0;

function runSecurityCheck(name, checkFunction) {
  totalChecks++;
  console.log(`🔍 ${name}...`);
  
  try {
    const result = checkFunction();
    if (result.passed) {
      passedChecks++;
      console.log(`✅ ${name}: SECURE`);
      if (result.message) console.log(`   ${result.message}`);
    } else {
      if (result.critical) {
        criticalIssues++;
        console.log(`❌ ${name}: CRITICAL SECURITY ISSUE`);
      } else {
        warnings++;
        console.log(`⚠️  ${name}: SECURITY WARNING`);
      }
      console.log(`   ${result.message}`);
    }
  } catch (error) {
    criticalIssues++;
    console.log(`❌ ${name}: AUDIT ERROR - ${error.message}`);
  }
  
  console.log('');
}

// Security Audit Checks
runSecurityCheck('Environment Variable Security', () => {
  const sensitiveVars = [
    'SUPABASE_SERVICE_ROLE_KEY',
    'JWT_SECRET',
    'ENCRYPTION_KEY',
    'SQUARE_ACCESS_TOKEN',
    'NEXTAUTH_SECRET'
  ];
  
  const exposedVars = sensitiveVars.filter(varName => {
    const value = process.env[varName];
    return value && value.startsWith('NEXT_PUBLIC_');
  });
  
  if (exposedVars.length > 0) {
    return { 
      passed: false, 
      critical: true, 
      message: `Sensitive variables exposed to client: ${exposedVars.join(', ')}` 
    };
  }
  
  const missingVars = sensitiveVars.filter(varName => !process.env[varName]);
  if (missingVars.length > 0) {
    return { 
      passed: false, 
      critical: true, 
      message: `Missing sensitive variables: ${missingVars.join(', ')}` 
    };
  }
  
  return { passed: true, message: 'All sensitive variables properly secured' };
});

runSecurityCheck('Production Configuration', () => {
  const nodeEnv = process.env.NODE_ENV;
  const debugMode = process.env.NEXT_PUBLIC_DEBUG_MODE;
  const devBypass = process.env.DEV_BYPASS_AUTH;
  const adminAccess = process.env.NEXT_PUBLIC_ADMIN_ACCESS;
  
  if (nodeEnv !== 'production') {
    return { passed: false, critical: true, message: 'NODE_ENV must be "production"' };
  }
  
  if (debugMode === 'true') {
    return { passed: false, critical: true, message: 'Debug mode enabled in production' };
  }
  
  if (devBypass === 'true') {
    return { passed: false, critical: true, message: 'Development auth bypass enabled - CRITICAL SECURITY RISK!' };
  }
  
  if (adminAccess !== 'true') {
    return { passed: false, critical: true, message: 'Admin access not properly configured' };
  }
  
  return { passed: true, message: 'Production configuration secure' };
});

runSecurityCheck('Authentication Security', () => {
  const jwtSecret = process.env.JWT_SECRET;
  const encryptionKey = process.env.ENCRYPTION_KEY;
  const nextAuthSecret = process.env.NEXTAUTH_SECRET;
  
  if (!jwtSecret || jwtSecret.length < 32) {
    return { passed: false, critical: true, message: 'JWT_SECRET too short or missing' };
  }
  
  if (!encryptionKey || encryptionKey.length !== 32) {
    return { passed: false, critical: true, message: 'ENCRYPTION_KEY must be exactly 32 characters' };
  }
  
  if (!nextAuthSecret || nextAuthSecret.length < 32) {
    return { passed: false, critical: true, message: 'NEXTAUTH_SECRET too short or missing' };
  }
  
  // Check for weak patterns
  const weakPatterns = ['12345', 'password', 'admin', 'test', 'demo'];
  const hasWeakPattern = weakPatterns.some(pattern => 
    jwtSecret.toLowerCase().includes(pattern) ||
    encryptionKey.toLowerCase().includes(pattern) ||
    nextAuthSecret.toLowerCase().includes(pattern)
  );
  
  if (hasWeakPattern) {
    return { passed: false, critical: true, message: 'Weak patterns detected in authentication secrets' };
  }
  
  return { passed: true, message: 'Authentication secrets are strong and properly configured' };
});

runSecurityCheck('Database Security', () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseUrl.startsWith('https://')) {
    return { passed: false, critical: true, message: 'Supabase URL must use HTTPS' };
  }
  
  if (!serviceKey || serviceKey.length < 100) {
    return { passed: false, critical: true, message: 'Invalid Supabase service role key' };
  }
  
  if (serviceKey.startsWith('NEXT_PUBLIC_')) {
    return { passed: false, critical: true, message: 'Service role key exposed to client' };
  }
  
  return { passed: true, message: 'Database configuration secure' };
});

runSecurityCheck('Payment Security', () => {
  const squareAppId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID;
  const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN;
  const squareEnv = process.env.NEXT_PUBLIC_SQUARE_ENVIRONMENT;
  
  if (!squareAppId || !squareAccessToken) {
    return { passed: false, message: 'Square payment configuration incomplete' };
  }
  
  if (squareEnv !== 'production') {
    return { passed: false, message: 'Square environment not set to production' };
  }
  
  if (squareAccessToken.startsWith('NEXT_PUBLIC_')) {
    return { passed: false, critical: true, message: 'Square access token exposed to client' };
  }
  
  if (squareAccessToken.includes('sandbox')) {
    return { passed: false, critical: true, message: 'Sandbox Square token in production' };
  }
  
  return { passed: true, message: 'Payment configuration secure for production' };
});

runSecurityCheck('File Permissions and Structure', () => {
  const sensitiveFiles = [
    '.env.local',
    'scripts/check-env.js',
    'scripts/deploy-admin.js',
    'lib/auth/admin-auth.ts'
  ];
  
  const missingFiles = sensitiveFiles.filter(file => 
    !fs.existsSync(path.join(__dirname, '..', file))
  );
  
  if (missingFiles.length > 0) {
    return { 
      passed: false, 
      critical: true, 
      message: `Missing critical files: ${missingFiles.join(', ')}` 
    };
  }
  
  // Check for exposed sensitive files in public directory
  const publicDir = path.join(__dirname, '..', 'public');
  if (fs.existsSync(publicDir)) {
    const publicFiles = fs.readdirSync(publicDir, { recursive: true });
    const exposedSensitive = publicFiles.filter(file => 
      file.includes('.env') || file.includes('config') || file.includes('secret')
    );
    
    if (exposedSensitive.length > 0) {
      return { 
        passed: false, 
        critical: true, 
        message: `Sensitive files in public directory: ${exposedSensitive.join(', ')}` 
      };
    }
  }
  
  return { passed: true, message: 'File structure and permissions secure' };
});

runSecurityCheck('Security Headers Configuration', () => {
  const nextConfigPath = path.join(__dirname, '..', 'next.config.js');
  const vercelConfigPath = path.join(__dirname, '..', 'vercel.json');
  
  if (!fs.existsSync(nextConfigPath) && !fs.existsSync(vercelConfigPath)) {
    return { passed: false, critical: true, message: 'No security headers configuration found' };
  }
  
  let configContent = '';
  if (fs.existsSync(nextConfigPath)) {
    configContent += fs.readFileSync(nextConfigPath, 'utf8');
  }
  if (fs.existsSync(vercelConfigPath)) {
    configContent += fs.readFileSync(vercelConfigPath, 'utf8');
  }
  
  const requiredHeaders = [
    'X-Frame-Options',
    'X-Content-Type-Options',
    'Strict-Transport-Security',
    'Content-Security-Policy'
  ];
  
  const missingHeaders = requiredHeaders.filter(header => 
    !configContent.includes(header)
  );
  
  if (missingHeaders.length > 0) {
    return { 
      passed: false, 
      message: `Missing security headers: ${missingHeaders.join(', ')}` 
    };
  }
  
  // Check for insecure CSP directives
  if (configContent.includes("'unsafe-eval'") && !configContent.includes("script-src")) {
    return { 
      passed: false, 
      message: 'Potentially insecure Content Security Policy detected' 
    };
  }
  
  return { passed: true, message: 'Security headers properly configured' };
});

runSecurityCheck('Dependency Security', () => {
  const packagePath = path.join(__dirname, '..', 'package.json');
  
  if (!fs.existsSync(packagePath)) {
    return { passed: false, critical: true, message: 'package.json not found' };
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  // Check for known vulnerable packages (basic check)
  const potentiallyVulnerable = [
    'lodash',
    'moment',
    'request',
    'node-sass'
  ];
  
  const foundVulnerable = potentiallyVulnerable.filter(pkg => dependencies[pkg]);
  
  if (foundVulnerable.length > 0) {
    return { 
      passed: false, 
      message: `Potentially vulnerable dependencies found: ${foundVulnerable.join(', ')}. Run npm audit for details.` 
    };
  }
  
  return { passed: true, message: 'No obviously vulnerable dependencies detected' };
});

// Summary
console.log('📊 SECURITY AUDIT SUMMARY');
console.log('==========================');
console.log(`Total Security Checks: ${totalChecks}`);
console.log(`Passed: ${passedChecks}`);
console.log(`Warnings: ${warnings}`);
console.log(`Critical Issues: ${criticalIssues}`);
console.log('');

if (criticalIssues > 0) {
  console.log('❌ SECURITY AUDIT FAILED');
  console.log('Critical security issues must be resolved before production deployment.');
  console.log('Please fix the issues above and run the audit again.');
  process.exit(1);
} else if (warnings > 0) {
  console.log('⚠️  SECURITY AUDIT PASSED WITH WARNINGS');
  console.log('Consider addressing the warnings for optimal security.');
} else {
  console.log('✅ SECURITY AUDIT PASSED');
  console.log('All security checks passed. Admin subdomain is secure for production.');
}

console.log('\n🔒 Security Recommendations:');
console.log('- Regularly update dependencies');
console.log('- Monitor audit logs daily');
console.log('- Implement IP restrictions if needed');
console.log('- Set up automated security scanning');
console.log('- Review access permissions monthly');
console.log('- Enable MFA for all admin users');

process.exit(criticalIssues > 0 ? 1 : 0);
