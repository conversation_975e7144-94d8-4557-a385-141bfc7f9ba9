/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_3__]);\nreact_toastify__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction AdminApp({ Component, pageProps }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Security: Disable right-click context menu in production\n        if (false) {}\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Security: Clear console in production\n        if (false) {}\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Content-Type-Options\",\n                        content: \"nosniff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Frame-Options\",\n                        content: \"DENY\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-XSS-Protection\",\n                        content: \"1; mode=block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"referrer\",\n                        content: \"strict-origin-when-cross-origin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex, nofollow, noarchive, nosnippet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"googlebot\",\n                        content: \"noindex, nofollow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Ocean Soul Sparkles Admin Portal - Secure staff access only\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/admin/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/admin/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/admin/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/admin/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3788d8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#3788d8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://js.squareup.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://api.onesignal.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"Content-Security-Policy\",\n                        content: \"default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.squareup.com https://cdn.onesignal.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.supabase.co https://js.squareup.com https://api.onesignal.com wss://*.supabase.co; frame-src 'self' https://js.squareup.com;\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Ocean Soul Sparkles Admin Portal\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                position: \"top-right\",\n                autoClose: 5000,\n                hideProgressBar: false,\n                newestOnTop: false,\n                closeOnClick: true,\n                rtl: false,\n                pauseOnFocusLoss: true,\n                draggable: true,\n                pauseOnHover: true,\n                theme: \"light\",\n                toastStyle: {\n                    fontFamily: \"inherit\",\n                    fontSize: \"14px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n             false && /*#__PURE__*/ 0,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: \"0\",\n                    left: \"0\",\n                    right: \"0\",\n                    background: \"#ff6b6b\",\n                    color: \"white\",\n                    padding: \"4px\",\n                    textAlign: \"center\",\n                    fontSize: \"12px\",\n                    fontWeight: \"bold\",\n                    zIndex: 10000\n                },\n                children: \"\\uD83D\\uDEA7 DEVELOPMENT MODE - ADMIN PORTAL \\uD83D\\uDEA7\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website-editor-package\\\\home\\\\ubuntu\\\\website-editor\\\\website-ocean-soul-sparkles\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react-toastify":
/*!*********************************!*\
  !*** external "react-toastify" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-toastify");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/react-toastify"], () => (__webpack_exec__("./pages/_app.tsx")));
module.exports = __webpack_exports__;

})();