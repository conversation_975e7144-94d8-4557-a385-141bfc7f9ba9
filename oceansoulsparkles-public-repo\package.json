{"name": "oceansoulsparkles-public", "version": "1.0.0", "description": "Ocean Soul Sparkles - Public Website (Customer-facing only)", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "next": "14.2.30", "react": "^18.2.0", "react-dom": "^18.2.0", "react-toastify": "^9.1.3", "react-onesignal": "^2.0.4", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-next": "14.0.4"}, "engines": {"node": ">=18.0.0"}}