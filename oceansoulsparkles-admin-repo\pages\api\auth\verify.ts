import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../lib/auth/admin-auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get token from cookie or Authorization header
    const token = req.cookies['admin-token'] || 
                 req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'No authentication token provided' });
    }

    // Verify the token
    const authResult = await verifyAdminToken(token);

    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: authResult.error || 'Invalid token' });
    }

    // Return user information
    return res.status(200).json({
      valid: true,
      user: authResult.user
    });

  } catch (error) {
    console.error('Token verification error:', error);
    return res.status(401).json({ error: 'Token verification failed' });
  }
}
