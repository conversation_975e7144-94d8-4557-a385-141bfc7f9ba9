#!/usr/bin/env node

/**
 * Admin Subdomain Deployment Validation Script
 * Comprehensive security and readiness checks before deployment
 */

const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

console.log('🔒 Ocean Soul Sparkles Admin Subdomain Deployment Validation\n');

let totalChecks = 0;
let passedChecks = 0;
let warnings = 0;
let criticalIssues = 0;

function runCheck(name, checkFunction) {
  totalChecks++;
  console.log(`🔍 ${name}...`);
  
  try {
    const result = checkFunction();
    if (result.passed) {
      passedChecks++;
      console.log(`✅ ${name}: PASSED`);
      if (result.message) console.log(`   ${result.message}`);
    } else {
      if (result.critical) {
        criticalIssues++;
        console.log(`❌ ${name}: CRITICAL FAILURE`);
      } else {
        warnings++;
        console.log(`⚠️  ${name}: WARNING`);
      }
      console.log(`   ${result.message}`);
    }
  } catch (error) {
    criticalIssues++;
    console.log(`❌ ${name}: ERROR - ${error.message}`);
  }
  
  console.log('');
}

// Security Checks
runCheck('Admin Subdomain Configuration', () => {
  const isAdminSubdomain = process.env.NEXT_PUBLIC_ADMIN_SUBDOMAIN === 'true';
  const hasAdminUrl = process.env.NEXT_PUBLIC_SITE_URL?.includes('admin.');
  
  if (!isAdminSubdomain) {
    return { passed: false, critical: true, message: 'NEXT_PUBLIC_ADMIN_SUBDOMAIN must be "true"' };
  }
  
  if (!hasAdminUrl) {
    return { passed: false, critical: true, message: 'NEXT_PUBLIC_SITE_URL must be admin subdomain URL' };
  }
  
  return { passed: true, message: 'Admin subdomain correctly configured' };
});

runCheck('Authentication Security', () => {
  const jwtSecret = process.env.JWT_SECRET;
  const encryptionKey = process.env.ENCRYPTION_KEY;
  const nextAuthSecret = process.env.NEXTAUTH_SECRET;
  
  if (!jwtSecret || jwtSecret.length < 32) {
    return { passed: false, critical: true, message: 'JWT_SECRET missing or too short (min 32 chars)' };
  }
  
  if (!encryptionKey || encryptionKey.length !== 32) {
    return { passed: false, critical: true, message: 'ENCRYPTION_KEY must be exactly 32 characters' };
  }
  
  if (!nextAuthSecret || nextAuthSecret.length < 32) {
    return { passed: false, critical: true, message: 'NEXTAUTH_SECRET missing or too short' };
  }
  
  return { passed: true, message: 'Authentication secrets properly configured' };
});

runCheck('Database Security', () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !anonKey || !serviceKey) {
    return { passed: false, critical: true, message: 'Missing Supabase configuration' };
  }
  
  if (serviceKey.startsWith('NEXT_PUBLIC_')) {
    return { passed: false, critical: true, message: 'Service role key must not be exposed to client' };
  }
  
  return { passed: true, message: 'Database configuration secure' };
});

runCheck('Payment Integration Security', () => {
  const squareAppId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID;
  const squareLocationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID;
  const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN;
  
  if (!squareAppId || !squareLocationId) {
    return { passed: false, message: 'Square public configuration missing (non-critical for admin)' };
  }
  
  if (!squareAccessToken) {
    return { passed: false, message: 'Square access token missing (required for payment processing)' };
  }
  
  if (squareAccessToken.startsWith('NEXT_PUBLIC_')) {
    return { passed: false, critical: true, message: 'Square access token must not be exposed to client' };
  }
  
  return { passed: true, message: 'Payment integration properly secured' };
});

runCheck('Development Flags Check', () => {
  const debugMode = process.env.NEXT_PUBLIC_DEBUG_MODE;
  const devBypass = process.env.DEV_BYPASS_AUTH;
  const mockPayments = process.env.DEV_MOCK_PAYMENTS;
  const nodeEnv = process.env.NODE_ENV;
  
  if (nodeEnv === 'production') {
    if (debugMode === 'true') {
      return { passed: false, critical: true, message: 'DEBUG_MODE enabled in production' };
    }
    
    if (devBypass === 'true') {
      return { passed: false, critical: true, message: 'DEV_BYPASS_AUTH enabled in production - SECURITY RISK!' };
    }
    
    if (mockPayments === 'true') {
      return { passed: false, critical: true, message: 'DEV_MOCK_PAYMENTS enabled in production' };
    }
  }
  
  return { passed: true, message: 'No development flags enabled in production' };
});

runCheck('File Structure Validation', () => {
  const requiredFiles = [
    'middleware.ts',
    'pages/admin/login.tsx',
    'pages/admin/dashboard.tsx',
    'pages/api/auth/login.ts',
    'pages/api/auth/verify.ts',
    'lib/auth/admin-auth.ts',
    'lib/security/audit-logging.ts',
    'components/admin/AdminLayout.tsx'
  ];
  
  const missingFiles = requiredFiles.filter(file => 
    !fs.existsSync(path.join(__dirname, '..', file))
  );
  
  if (missingFiles.length > 0) {
    return { 
      passed: false, 
      critical: true, 
      message: `Missing required files: ${missingFiles.join(', ')}` 
    };
  }
  
  return { passed: true, message: 'All required files present' };
});

runCheck('Security Headers Configuration', () => {
  const nextConfigPath = path.join(__dirname, '..', 'next.config.js');
  
  if (!fs.existsSync(nextConfigPath)) {
    return { passed: false, critical: true, message: 'next.config.js not found' };
  }
  
  const nextConfig = fs.readFileSync(nextConfigPath, 'utf8');
  
  const requiredHeaders = [
    'X-Frame-Options',
    'X-Content-Type-Options',
    'Referrer-Policy',
    'Content-Security-Policy'
  ];
  
  const missingHeaders = requiredHeaders.filter(header => 
    !nextConfig.includes(header)
  );
  
  if (missingHeaders.length > 0) {
    return { 
      passed: false, 
      message: `Missing security headers: ${missingHeaders.join(', ')}` 
    };
  }
  
  return { passed: true, message: 'Security headers properly configured' };
});

runCheck('Admin Route Protection', () => {
  const middlewarePath = path.join(__dirname, '..', 'middleware.ts');
  
  if (!fs.existsSync(middlewarePath)) {
    return { passed: false, critical: true, message: 'middleware.ts not found' };
  }
  
  const middleware = fs.readFileSync(middlewarePath, 'utf8');
  
  if (!middleware.includes('PROTECTED_ROUTES')) {
    return { passed: false, critical: true, message: 'Route protection not implemented' };
  }
  
  if (!middleware.includes('verifyAdminToken')) {
    return { passed: false, critical: true, message: 'Token verification not implemented' };
  }
  
  return { passed: true, message: 'Admin routes properly protected' };
});

runCheck('Audit Logging Configuration', () => {
  const auditPath = path.join(__dirname, '..', 'lib/security/audit-logging.ts');
  
  if (!fs.existsSync(auditPath)) {
    return { passed: false, critical: true, message: 'Audit logging module not found' };
  }
  
  const auditCode = fs.readFileSync(auditPath, 'utf8');
  
  if (!auditCode.includes('audit_logs')) {
    return { passed: false, message: 'Audit logs table not configured' };
  }
  
  return { passed: true, message: 'Audit logging properly configured' };
});

runCheck('Environment Variables Validation', () => {
  const requiredVars = [
    'NEXT_PUBLIC_ADMIN_SUBDOMAIN',
    'NEXT_PUBLIC_SITE_URL',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'JWT_SECRET',
    'ENCRYPTION_KEY'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    return { 
      passed: false, 
      critical: true, 
      message: `Missing environment variables: ${missingVars.join(', ')}` 
    };
  }
  
  return { passed: true, message: 'All required environment variables set' };
});

runCheck('Build Configuration', () => {
  const packagePath = path.join(__dirname, '..', 'package.json');
  
  if (!fs.existsSync(packagePath)) {
    return { passed: false, critical: true, message: 'package.json not found' };
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  if (!packageJson.scripts?.build?.includes('check-env.js')) {
    return { passed: false, message: 'Environment check not integrated in build process' };
  }
  
  return { passed: true, message: 'Build configuration includes environment validation' };
});

// Summary
console.log('📊 DEPLOYMENT VALIDATION SUMMARY');
console.log('================================');
console.log(`Total Checks: ${totalChecks}`);
console.log(`Passed: ${passedChecks}`);
console.log(`Warnings: ${warnings}`);
console.log(`Critical Issues: ${criticalIssues}`);
console.log('');

if (criticalIssues > 0) {
  console.log('❌ DEPLOYMENT BLOCKED');
  console.log('Critical security issues must be resolved before deployment.');
  console.log('Please fix the issues above and run the validation again.');
  process.exit(1);
} else if (warnings > 0) {
  console.log('⚠️  DEPLOYMENT ALLOWED WITH WARNINGS');
  console.log('Consider addressing the warnings for optimal security and functionality.');
} else {
  console.log('✅ DEPLOYMENT APPROVED');
  console.log('All security checks passed. Admin subdomain is ready for production deployment.');
}

console.log('\n🚀 Next Steps:');
console.log('1. Run: npm run build');
console.log('2. Deploy to Vercel with admin subdomain configuration');
console.log('3. Configure DNS for admin.oceansoulsparkles.com.au');
console.log('4. Test all authentication and security features');
console.log('5. Monitor audit logs and system performance');

console.log('\n🔒 Security Reminders:');
console.log('- Enable IP restrictions if required');
console.log('- Set up monitoring and alerting');
console.log('- Regularly review audit logs');
console.log('- Keep all dependencies updated');
console.log('- Test MFA functionality');

process.exit(criticalIssues > 0 ? 1 : 0);
