import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '@/components/Layout';
import { useCustomer } from '@/contexts/CustomerContext';
import { toast } from 'react-toastify';
import styles from '@/styles/Auth.module.css';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  const { signIn, isAuthenticated, loading: authLoading } = useCustomer();
  const router = useRouter();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      const redirectTo = router.query.redirect || '/';
      router.replace(redirectTo);
    }
  }, [isAuthenticated, authLoading, router]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast.error('Please fill in all fields');
      return;
    }

    setLoading(true);

    try {
      const result = await signIn(email, password);
      
      if (result.success) {
        const redirectTo = router.query.redirect || '/';
        router.push(redirectTo);
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Layout>
        <div className={styles.authContainer}>
          <div className={styles.authCard}>
            <div className={styles.loadingSpinner}>
              <div className={styles.spinner}></div>
              <p>Loading...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Don't render login form if already authenticated
  if (isAuthenticated) {
    return null;
  }

  return (
    <Layout>
      <Head>
        <title>Customer Login - Ocean Soul Sparkles</title>
        <meta name="description" content="Sign in to your Ocean Soul Sparkles customer account to manage bookings and view order history." />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className={styles.authContainer}>
        <div className={styles.authCard}>
          <div className={styles.authHeader}>
            <h1>Welcome Back</h1>
            <p>Sign in to your customer account</p>
          </div>

          <form onSubmit={handleSubmit} className={styles.authForm}>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email Address</label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                required
                disabled={loading}
                className={styles.formInput}
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="password">Password</label>
              <div className={styles.passwordInput}>
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  required
                  disabled={loading}
                  className={styles.formInput}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className={styles.passwordToggle}
                  disabled={loading}
                >
                  {showPassword ? '👁️' : '👁️‍🗨️'}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className={`${styles.authButton} ${loading ? styles.loading : ''}`}
            >
              {loading ? (
                <>
                  <span className={styles.buttonSpinner}></span>
                  Signing In...
                </>
              ) : (
                'Sign In'
              )}
            </button>
          </form>

          <div className={styles.authLinks}>
            <p>
              Don't have an account?{' '}
              <Link href="/signup" className={styles.authLink}>
                Create Account
              </Link>
            </p>
            <p>
              <Link href="/forgot-password" className={styles.authLink}>
                Forgot your password?
              </Link>
            </p>
          </div>

          <div className={styles.guestOption}>
            <div className={styles.divider}>
              <span>or</span>
            </div>
            <p>
              Want to book without an account?{' '}
              <Link href="/book-online" className={styles.authLink}>
                Continue as Guest
              </Link>
            </p>
          </div>
        </div>

        <div className={styles.authFooter}>
          <p>
            By signing in, you agree to our{' '}
            <Link href="/policies#terms" className={styles.authLink}>
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="/policies#privacy" className={styles.authLink}>
              Privacy Policy
            </Link>
          </p>
        </div>
      </div>
    </Layout>
  );
}
