import { useState } from 'react';
import { toast } from 'react-toastify';
import styles from '@/styles/BookingForm.module.css';

export default function BookingForm({ 
  service, 
  customer, 
  isAuthenticated, 
  onBookingComplete, 
  onCancel 
}) {
  const [formData, setFormData] = useState({
    // Customer information
    firstName: customer?.user_metadata?.first_name || '',
    lastName: customer?.user_metadata?.last_name || '',
    email: customer?.email || '',
    phone: customer?.user_metadata?.phone || '',
    
    // Booking details
    preferredDate: '',
    preferredTime: '',
    alternativeDate: '',
    alternativeTime: '',
    eventType: '',
    numberOfPeople: '1',
    location: '',
    address: '',
    specialRequests: '',
    
    // Agreement
    agreeToTerms: false
  });

  const [loading, setLoading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const validateForm = () => {
    const required = ['firstName', 'lastName', 'email', 'phone', 'preferredDate', 'preferredTime', 'eventType', 'location'];
    
    for (const field of required) {
      if (!formData[field]) {
        toast.error(`Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
        return false;
      }
    }

    if (!formData.agreeToTerms) {
      toast.error('Please agree to the terms and conditions');
      return false;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error('Please enter a valid email address');
      return false;
    }

    // Validate date is in the future
    const selectedDate = new Date(formData.preferredDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (selectedDate < today) {
      toast.error('Please select a future date');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Prepare booking data
      const bookingData = {
        service_id: service.id,
        service_name: service.name,
        customer_info: {
          first_name: formData.firstName,
          last_name: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          is_authenticated: isAuthenticated,
          customer_id: customer?.id || null
        },
        booking_details: {
          preferred_date: formData.preferredDate,
          preferred_time: formData.preferredTime,
          alternative_date: formData.alternativeDate || null,
          alternative_time: formData.alternativeTime || null,
          event_type: formData.eventType,
          number_of_people: parseInt(formData.numberOfPeople),
          location: formData.location,
          address: formData.address,
          special_requests: formData.specialRequests || null
        },
        pricing: {
          base_price: service.price,
          estimated_total: service.price * parseInt(formData.numberOfPeople)
        },
        status: 'pending',
        created_at: new Date().toISOString()
      };

      // Submit booking request
      const response = await fetch('/api/bookings/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit booking');
      }

      // Success
      toast.success('Booking request submitted successfully!');
      onBookingComplete();

    } catch (error) {
      console.error('Booking submission error:', error);
      toast.error(error.message || 'Failed to submit booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.bookingFormContainer}>
      <form onSubmit={handleSubmit} className={styles.bookingForm}>
        {/* Customer Information */}
        <div className={styles.formSection}>
          <h3>Your Information</h3>
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="firstName">First Name *</label>
              <input
                id="firstName"
                name="firstName"
                type="text"
                value={formData.firstName}
                onChange={handleInputChange}
                required
                disabled={loading}
                className={styles.formInput}
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="lastName">Last Name *</label>
              <input
                id="lastName"
                name="lastName"
                type="text"
                value={formData.lastName}
                onChange={handleInputChange}
                required
                disabled={loading}
                className={styles.formInput}
              />
            </div>
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email Address *</label>
              <input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                disabled={loading}
                className={styles.formInput}
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="phone">Phone Number *</label>
              <input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleInputChange}
                required
                disabled={loading}
                className={styles.formInput}
              />
            </div>
          </div>
        </div>

        {/* Booking Details */}
        <div className={styles.formSection}>
          <h3>Booking Details</h3>
          
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="preferredDate">Preferred Date *</label>
              <input
                id="preferredDate"
                name="preferredDate"
                type="date"
                value={formData.preferredDate}
                onChange={handleInputChange}
                min={new Date().toISOString().split('T')[0]}
                required
                disabled={loading}
                className={styles.formInput}
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="preferredTime">Preferred Time *</label>
              <input
                id="preferredTime"
                name="preferredTime"
                type="time"
                value={formData.preferredTime}
                onChange={handleInputChange}
                required
                disabled={loading}
                className={styles.formInput}
              />
            </div>
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="alternativeDate">Alternative Date</label>
              <input
                id="alternativeDate"
                name="alternativeDate"
                type="date"
                value={formData.alternativeDate}
                onChange={handleInputChange}
                min={new Date().toISOString().split('T')[0]}
                disabled={loading}
                className={styles.formInput}
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="alternativeTime">Alternative Time</label>
              <input
                id="alternativeTime"
                name="alternativeTime"
                type="time"
                value={formData.alternativeTime}
                onChange={handleInputChange}
                disabled={loading}
                className={styles.formInput}
              />
            </div>
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="eventType">Event Type *</label>
              <select
                id="eventType"
                name="eventType"
                value={formData.eventType}
                onChange={handleInputChange}
                required
                disabled={loading}
                className={styles.formInput}
              >
                <option value="">Select event type</option>
                <option value="birthday-party">Birthday Party</option>
                <option value="festival">Festival</option>
                <option value="corporate-event">Corporate Event</option>
                <option value="wedding">Wedding</option>
                <option value="school-event">School Event</option>
                <option value="private-session">Private Session</option>
                <option value="other">Other</option>
              </select>
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="numberOfPeople">Number of People *</label>
              <select
                id="numberOfPeople"
                name="numberOfPeople"
                value={formData.numberOfPeople}
                onChange={handleInputChange}
                required
                disabled={loading}
                className={styles.formInput}
              >
                {[...Array(20)].map((_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {i + 1} {i === 0 ? 'person' : 'people'}
                  </option>
                ))}
                <option value="20+">20+ people</option>
              </select>
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="location">Location Type *</label>
            <select
              id="location"
              name="location"
              value={formData.location}
              onChange={handleInputChange}
              required
              disabled={loading}
              className={styles.formInput}
            >
              <option value="">Select location</option>
              <option value="home">Private Home</option>
              <option value="venue">Event Venue</option>
              <option value="park">Park/Outdoor</option>
              <option value="school">School</option>
              <option value="office">Office/Workplace</option>
              <option value="studio">Our Studio</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="address">Address/Location Details</label>
            <textarea
              id="address"
              name="address"
              value={formData.address}
              onChange={handleInputChange}
              placeholder="Please provide the full address or location details"
              disabled={loading}
              className={styles.formTextarea}
              rows={3}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="specialRequests">Special Requests</label>
            <textarea
              id="specialRequests"
              name="specialRequests"
              value={formData.specialRequests}
              onChange={handleInputChange}
              placeholder="Any special requests, themes, or requirements?"
              disabled={loading}
              className={styles.formTextarea}
              rows={4}
            />
          </div>
        </div>

        {/* Pricing Estimate */}
        <div className={styles.pricingEstimate}>
          <h3>Pricing Estimate</h3>
          <div className={styles.pricingRow}>
            <span>Base Price ({service.name}):</span>
            <span>${service.price}</span>
          </div>
          <div className={styles.pricingRow}>
            <span>Number of People:</span>
            <span>{formData.numberOfPeople}</span>
          </div>
          <div className={styles.pricingTotal}>
            <span>Estimated Total:</span>
            <span>${service.price * parseInt(formData.numberOfPeople || 1)}</span>
          </div>
          <p className={styles.pricingNote}>
            *Final pricing may vary based on location, duration, and specific requirements. 
            We'll provide a detailed quote after reviewing your request.
          </p>
        </div>

        {/* Terms Agreement */}
        <div className={styles.termsSection}>
          <label className={styles.checkboxLabel}>
            <input
              type="checkbox"
              name="agreeToTerms"
              checked={formData.agreeToTerms}
              onChange={handleInputChange}
              required
              disabled={loading}
              className={styles.checkbox}
            />
            <span>
              I agree to the{' '}
              <a href="/policies#terms" target="_blank" rel="noopener noreferrer">
                Terms of Service
              </a>{' '}
              and{' '}
              <a href="/policies#privacy" target="_blank" rel="noopener noreferrer">
                Privacy Policy
              </a>
            </span>
          </label>
        </div>

        {/* Form Actions */}
        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onCancel}
            disabled={loading}
            className={styles.cancelButton}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className={styles.submitButton}
          >
            {loading ? 'Submitting...' : 'Submit Booking Request'}
          </button>
        </div>
      </form>
    </div>
  );
}
