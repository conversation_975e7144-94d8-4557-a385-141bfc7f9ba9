/* Booking Form Styles */
.bookingFormContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.bookingForm {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.formSection {
  margin-bottom: 32px;
}

.formSection h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e9ecef;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #2c3e50;
}

.formInput,
.formTextarea {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.formInput:focus,
.formTextarea:focus {
  outline: none;
  border-color: #3788d8;
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.formTextarea {
  resize: vertical;
  min-height: 80px;
}

.pricingEstimate {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 24px 0;
}

.pricingEstimate h3 {
  margin-bottom: 16px;
  color: #2c3e50;
}

.pricingRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.pricingTotal {
  display: flex;
  justify-content: space-between;
  font-weight: 700;
  font-size: 18px;
  color: #2c3e50;
  border-top: 2px solid #dee2e6;
  padding-top: 12px;
  margin-top: 12px;
}

.pricingNote {
  font-size: 14px;
  color: #6c757d;
  margin-top: 12px;
  font-style: italic;
}

.termsSection {
  margin: 24px 0;
}

.checkboxLabel {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
}

.checkbox {
  margin-top: 2px;
  width: 16px;
  height: 16px;
  accent-color: #3788d8;
}

.formActions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
}

.cancelButton {
  padding: 12px 24px;
  border: 2px solid #6c757d;
  background: transparent;
  color: #6c757d;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  background: #6c757d;
  color: white;
}

.submitButton {
  padding: 12px 24px;
  border: none;
  background: linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submitButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 136, 216, 0.3);
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .bookingFormContainer {
    padding: 16px;
  }

  .bookingForm {
    padding: 24px;
  }

  .formRow {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .formActions {
    flex-direction: column;
  }

  .cancelButton,
  .submitButton {
    width: 100%;
  }
}
