(()=>{var e={};e.id=888,e.ids=[888],e.modules={6814:(e,t,s)=>{"use strict";s.a(e,async(e,n)=>{try{s.r(t),s.d(t,{default:()=>p});var o=s(997),i=s(968),r=s.n(i);s(6689);var a=s(3590);s(8819),s(6764);var c=e([a]);function p({Component:e,pageProps:t}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(r(),{children:[o.jsx("meta",{charSet:"utf-8"}),o.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),o.jsx("meta",{httpEquiv:"X-Content-Type-Options",content:"nosniff"}),o.jsx("meta",{httpEquiv:"X-Frame-Options",content:"DENY"}),o.jsx("meta",{httpEquiv:"X-XSS-Protection",content:"1; mode=block"}),o.jsx("meta",{name:"referrer",content:"strict-origin-when-cross-origin"}),o.jsx("meta",{name:"robots",content:"noindex, nofollow, noarchive, nosnippet"}),o.jsx("meta",{name:"googlebot",content:"noindex, nofollow"}),o.jsx("meta",{name:"description",content:"Ocean Soul Sparkles Admin Portal - Secure staff access only"}),o.jsx("link",{rel:"icon",href:"/admin/favicon.ico"}),o.jsx("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/admin/apple-touch-icon.png"}),o.jsx("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/admin/favicon-32x32.png"}),o.jsx("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/admin/favicon-16x16.png"}),o.jsx("meta",{name:"theme-color",content:"#3788d8"}),o.jsx("meta",{name:"msapplication-TileColor",content:"#3788d8"}),o.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),o.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),o.jsx("link",{rel:"preconnect",href:"https://ndlgbcsbidyhxbpqzgqp.supabase.co"}),o.jsx("link",{rel:"dns-prefetch",href:"https://js.squareup.com"}),o.jsx("link",{rel:"dns-prefetch",href:"https://api.onesignal.com"}),o.jsx("meta",{httpEquiv:"Content-Security-Policy",content:"default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.squareup.com https://cdn.onesignal.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.supabase.co https://js.squareup.com https://api.onesignal.com wss://*.supabase.co; frame-src 'self' https://js.squareup.com;"}),o.jsx("title",{children:"Ocean Soul Sparkles Admin Portal"})]}),o.jsx(e,{...t}),o.jsx(a.ToastContainer,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light",toastStyle:{fontFamily:"inherit",fontSize:"14px"}}),o.jsx("div",{style:{position:"fixed",bottom:"10px",right:"10px",background:"rgba(0, 0, 0, 0.1)",color:"rgba(0, 0, 0, 0.3)",padding:"4px 8px",borderRadius:"4px",fontSize:"10px",fontWeight:"bold",pointerEvents:"none",zIndex:9999,userSelect:"none"},children:"ADMIN PORTAL"}),!1]})}a=(c.then?(await c)():c)[0],n()}catch(e){n(e)}})},8819:()=>{},6764:()=>{},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../webpack-runtime.js");t.C(e);var s=t(t.s=6814);module.exports=s})();