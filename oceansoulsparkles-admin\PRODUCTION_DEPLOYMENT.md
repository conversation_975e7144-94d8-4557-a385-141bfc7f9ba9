# Ocean Soul Sparkles Admin Subdomain - Production Deployment Guide

## 🚀 Deployment Status

**Current Status**: ✅ Ready for Production Deployment

All security checks passed and the application has been built successfully.

## 📋 Pre-Deployment Checklist

### ✅ Phase 1: Environment Configuration - COMPLETED
- [x] Production environment variables configured
- [x] Secure JWT and encryption keys generated
- [x] Production Square credentials configured
- [x] Supabase service role key configured
- [x] Security headers and CSP configured
- [x] Environment validation passed

### ✅ Phase 2: Database Security Verification - COMPLETED
- [x] Row Level Security (RLS) enabled on all tables
- [x] 5-tier role system verified (DEV, Admin, Artist, Braider, User)
- [x] Security helper functions confirmed
- [x] Audit logging configured

### ✅ Phase 3: Production Build - COMPLETED
- [x] Application built successfully
- [x] All deployment checks passed
- [x] vercel.json configuration created
- [x] Security validation completed

## 🌐 Vercel Deployment Instructions

### Step 1: Deploy to Vercel
```bash
# From the oceansoulsparkles-admin directory
vercel --prod
```

### Step 2: Configure Environment Variables in Vercel
Set the following environment variables in your Vercel project settings:

#### Required Variables
```
NEXT_PUBLIC_ADMIN_SUBDOMAIN=true
NEXT_PUBLIC_SITE_URL=https://admin.oceansoulsparkles.com.au
NEXT_PUBLIC_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzA1OTk5NywiZXhwIjoyMDYyNjM1OTk3fQ._9quzbWREyhdPiQiSUkzuqyBm8v4fkK2uqiswdt3AvY
NEXTAUTH_URL=https://admin.oceansoulsparkles.com.au
NEXTAUTH_SECRET=OSS_Admin_2025_Secure_NextAuth_Key_32_Chars_Min_Production
JWT_SECRET=OSS_JWT_Admin_Token_Secret_2025_Production_32_Characters
ENCRYPTION_KEY=OSS_Encrypt_Key_2025_Admin_32_Ch
```

#### Payment Integration
```
NEXT_PUBLIC_SQUARE_APPLICATION_ID=*****************************
NEXT_PUBLIC_SQUARE_LOCATION_ID=LBZPW61WHXG6F
SQUARE_ACCESS_TOKEN=****************************************************************
NEXT_PUBLIC_SQUARE_ENVIRONMENT=production
```

#### Security & MFA
```
MFA_ISSUER=Ocean Soul Sparkles Admin
MFA_ENCRYPTION_KEY=OSS_MFA_Encrypt_2025_Admin_32_Ch
SESSION_TIMEOUT=3600
ADMIN_SESSION_TIMEOUT=1800
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900
```

#### Production Configuration
```
NODE_ENV=production
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_ADMIN_ACCESS=true
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_DEBUG_AUTH=false
ENABLE_AUTH_BYPASS=false
```

### Step 3: Configure Custom Domain
1. In Vercel dashboard, go to your project settings
2. Navigate to "Domains" section
3. Add custom domain: `admin.oceansoulsparkles.com.au`
4. Configure DNS records as instructed by Vercel

### Step 4: DNS Configuration
Add the following DNS records to your domain provider:

```
Type: CNAME
Name: admin
Value: cname.vercel-dns.com
TTL: 300
```

## 🔒 Security Configuration

### SSL Certificate
- Vercel automatically provides SSL certificates
- Verify HTTPS is working: https://admin.oceansoulsparkles.com.au
- Check SSL rating at SSL Labs

### Security Headers
The following security headers are automatically configured:
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Referrer-Policy: strict-origin-when-cross-origin
- Strict-Transport-Security: max-age=********; includeSubDomains; preload
- Content-Security-Policy: [Configured for admin security]

### IP Restrictions (Optional)
To enable IP restrictions, add to environment variables:
```
ENABLE_IP_RESTRICTIONS=true
ADMIN_IP_WHITELIST=your.office.ip.address,another.allowed.ip
```

## 👥 Staff Account Creation

### Creating Admin Accounts
1. Access the admin portal at https://admin.oceansoulsparkles.com.au
2. Use DEV account to create initial Admin accounts
3. Admin accounts can then create Artist/Braider accounts
4. Follow the role hierarchy: DEV → Admin → Artist/Braider → User

### Initial User Setup
```sql
-- Example SQL to create initial admin user (run in Supabase SQL editor)
INSERT INTO user_roles (user_id, role, created_by, created_at)
VALUES (
  'user-uuid-here',
  'admin',
  'dev-user-uuid',
  NOW()
);
```

## 🧪 Post-Deployment Testing

### Authentication Testing
- [ ] Login with valid credentials
- [ ] MFA setup and verification
- [ ] Password reset functionality
- [ ] Session timeout behavior
- [ ] Role-based access control

### Core Functionality Testing
- [ ] Dashboard loads correctly
- [ ] Booking creation and management
- [ ] Customer database access
- [ ] Payment processing
- [ ] Report generation
- [ ] Notification system

### Security Testing
- [ ] Unauthorized access attempts blocked
- [ ] SQL injection protection
- [ ] XSS protection
- [ ] CSRF protection
- [ ] Rate limiting
- [ ] Audit logging

### Performance Testing
- [ ] Page load times < 3 seconds
- [ ] API response times < 500ms
- [ ] Database query optimization
- [ ] Image optimization
- [ ] CDN functionality

## 📊 Monitoring and Maintenance

### Performance Monitoring
- Set up Vercel Analytics
- Configure error tracking (Sentry)
- Monitor API response times
- Track user engagement metrics

### Security Monitoring
- Review audit logs daily
- Monitor failed login attempts
- Check for unusual access patterns
- Verify SSL certificate status

### Backup Procedures
- Database backups (automated via Supabase)
- Environment variable backups
- Code repository backups
- Documentation updates

## 🆘 Emergency Procedures

### Rollback Plan
1. Revert to previous Vercel deployment
2. Update DNS if necessary
3. Notify staff of temporary issues
4. Investigate and fix issues
5. Redeploy when ready

### Incident Response
1. Identify and contain the issue
2. Notify stakeholders
3. Document the incident
4. Implement fixes
5. Conduct post-incident review

## 📞 Support Contacts

**Technical Issues**: <EMAIL>
**Business Operations**: <EMAIL>
**Vercel Support**: https://vercel.com/support
**Supabase Support**: https://supabase.com/support

## 📅 Maintenance Schedule

### Daily
- Monitor system performance
- Review audit logs
- Check error reports

### Weekly
- Security updates
- Performance optimization
- Backup verification

### Monthly
- Full security audit
- Staff training updates
- Documentation review
- Dependency updates

---

## ✅ Deployment Completion Checklist

- [ ] Vercel deployment successful
- [ ] Custom domain configured
- [ ] SSL certificate active
- [ ] Environment variables set
- [ ] DNS records configured
- [ ] Authentication testing passed
- [ ] Core functionality verified
- [ ] Security testing completed
- [ ] Staff accounts created
- [ ] Monitoring configured
- [ ] Documentation updated
- [ ] Team training completed

**Deployment Date**: _______________
**Deployed By**: _______________
**Verified By**: _______________

---

*Keep this document updated with any changes to the deployment process.*
