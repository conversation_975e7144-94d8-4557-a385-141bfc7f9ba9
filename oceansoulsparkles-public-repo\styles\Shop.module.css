/* Shop Page Styles */
.shopContainer {
  padding-top: 80px; /* Account for fixed header */
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.shopHeader {
  text-align: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 40px;
}

.shopHeader h1 {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 700;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shopHeader p {
  font-size: clamp(1rem, 2vw, 1.3rem);
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
}

.shopControls {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 40px;
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.searchContainer {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.searchInput {
  width: 100%;
  padding: 12px 16px 12px 48px;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  font-size: 16px;
  background: white;
  transition: all 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #3788d8;
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.searchIcon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.categoryFilter {
  min-width: 200px;
}

.categorySelect {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.categorySelect:focus {
  outline: none;
  border-color: #3788d8;
}

.productsSection {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 60px;
}

.productsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 32px;
  margin-bottom: 60px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #6c757d;
}

.loadingSpinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #3788d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.noProducts {
  text-align: center;
  padding: 80px 20px;
  color: #6c757d;
}

.noProductsIcon {
  font-size: 5rem;
  margin-bottom: 24px;
  opacity: 0.5;
}

.noProducts h3 {
  color: #495057;
  font-size: 1.5rem;
  margin-bottom: 16px;
}

.noProducts p {
  margin-bottom: 32px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.1rem;
}

.clearFiltersButton {
  padding: 12px 24px;
  background: transparent;
  color: #3788d8;
  border: 2px solid #3788d8;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.clearFiltersButton:hover {
  background: #3788d8;
  color: white;
}

.ecoInfo {
  background: white;
  padding: 80px 0;
  margin: 60px 0;
}

.ecoInfoGrid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.ecoInfoCard {
  text-align: center;
  padding: 32px 24px;
  background: #f8f9fa;
  border-radius: 16px;
  transition: transform 0.3s ease;
}

.ecoInfoCard:hover {
  transform: translateY(-4px);
}

.ecoIcon {
  font-size: 3rem;
  margin-bottom: 20px;
  display: block;
}

.ecoInfoCard h3 {
  color: #2c3e50;
  font-size: 1.3rem;
  margin-bottom: 16px;
}

.ecoInfoCard p {
  color: #6c757d;
  line-height: 1.6;
}

.shippingInfo {
  background: linear-gradient(135deg, #4ECDC4 0%, #45b7aa 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.shippingInfo h3 {
  font-size: 2rem;
  margin-bottom: 40px;
  color: white;
}

.shippingGrid {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
}

.shippingCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 32px 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.shippingCard h4 {
  color: white;
  font-size: 1.2rem;
  margin-bottom: 12px;
}

.shippingCard p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.customOrders {
  background: white;
  padding: 80px 0;
  text-align: center;
}

.customOrders h3 {
  color: #2c3e50;
  font-size: 2rem;
  margin-bottom: 16px;
}

.customOrders p {
  color: #6c757d;
  font-size: 1.1rem;
  margin-bottom: 32px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.contactActions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .shopControls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .searchContainer {
    min-width: auto;
  }

  .categoryFilter {
    min-width: auto;
  }

  .productsGrid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
  }

  .ecoInfoGrid {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .shippingGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .contactActions {
    flex-direction: column;
    align-items: center;
  }

  .contactActions .button {
    width: 250px;
  }
}

@media (max-width: 480px) {
  .shopContainer {
    padding-top: 80px;
  }

  .shopHeader {
    padding: 40px 16px;
  }

  .shopControls {
    padding: 0 16px 32px;
  }

  .productsSection {
    padding: 0 16px 40px;
  }

  .productsGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .searchInput {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .ecoInfoCard {
    padding: 24px 20px;
  }

  .shippingCard {
    padding: 24px 20px;
  }
}
