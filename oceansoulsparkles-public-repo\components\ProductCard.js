import { useState } from 'react';
import Link from 'next/link';
import { toast } from 'react-toastify';
import styles from '@/styles/ProductCard.module.css';

export default function ProductCard({ product }) {
  const [imageError, setImageError] = useState(false);
  const [addingToCart, setAddingToCart] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  const handleAddToCart = async () => {
    setAddingToCart(true);
    
    try {
      // Simulate add to cart functionality
      // In a real implementation, this would integrate with your cart system
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast.success(`${product.name} added to cart!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add item to cart');
    } finally {
      setAddingToCart(false);
    }
  };

  const handleQuickView = () => {
    // In a real implementation, this would open a product modal
    toast.info('Quick view feature coming soon!');
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(price);
  };

  const getStockStatus = () => {
    if (product.stock_quantity === 0) {
      return { status: 'out-of-stock', text: 'Out of Stock', color: '#dc3545' };
    } else if (product.stock_quantity <= 5) {
      return { status: 'low-stock', text: `Only ${product.stock_quantity} left`, color: '#fd7e14' };
    } else {
      return { status: 'in-stock', text: 'In Stock', color: '#28a745' };
    }
  };

  const stockStatus = getStockStatus();

  return (
    <div className={styles.productCard}>
      <div className={styles.productImageContainer}>
        {!imageError && product.image_url ? (
          <img
            src={product.image_url}
            alt={product.name}
            className={styles.productImage}
            onError={handleImageError}
          />
        ) : (
          <div className={styles.placeholderImage}>
            <span className={styles.placeholderIcon}>🎨</span>
          </div>
        )}
        
        {/* Stock Status Badge */}
        <div 
          className={`${styles.stockBadge} ${styles[stockStatus.status]}`}
          style={{ backgroundColor: stockStatus.color }}
        >
          {stockStatus.text}
        </div>

        {/* Quick Actions Overlay */}
        <div className={styles.productOverlay}>
          <button
            onClick={handleQuickView}
            className={styles.quickViewButton}
            aria-label="Quick view"
          >
            👁️
          </button>
        </div>
      </div>

      <div className={styles.productContent}>
        <div className={styles.productHeader}>
          <h3 className={styles.productName}>{product.name}</h3>
          {product.category_name && (
            <span className={styles.productCategory}>{product.category_name}</span>
          )}
        </div>

        <p className={styles.productDescription}>{product.description}</p>

        {/* Product Features */}
        {product.features && product.features.length > 0 && (
          <div className={styles.productFeatures}>
            <ul>
              {product.features.slice(0, 2).map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Ingredients/Materials */}
        {product.ingredients && (
          <div className={styles.productIngredients}>
            <span className={styles.ingredientsLabel}>Made with:</span>
            <span className={styles.ingredientsText}>{product.ingredients}</span>
          </div>
        )}

        <div className={styles.productFooter}>
          <div className={styles.priceContainer}>
            <span className={styles.price}>{formatPrice(product.price)}</span>
            {product.original_price && product.original_price > product.price && (
              <span className={styles.originalPrice}>
                {formatPrice(product.original_price)}
              </span>
            )}
          </div>

          <div className={styles.productActions}>
            {product.stock_quantity > 0 ? (
              <button
                onClick={handleAddToCart}
                disabled={addingToCart}
                className={styles.addToCartButton}
              >
                {addingToCart ? (
                  <>
                    <span className={styles.buttonSpinner}></span>
                    Adding...
                  </>
                ) : (
                  'Add to Cart'
                )}
              </button>
            ) : (
              <button disabled className={styles.outOfStockButton}>
                Out of Stock
              </button>
            )}
          </div>
        </div>

        {/* Eco-Friendly Badge */}
        {product.is_eco_friendly && (
          <div className={styles.ecoBadge}>
            <span className={styles.ecoIcon}>🌿</span>
            <span>Eco-Friendly</span>
          </div>
        )}
      </div>
    </div>
  );
}
