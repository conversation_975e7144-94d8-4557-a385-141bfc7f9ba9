/**
 * Ocean Soul Sparkles - Public Supabase Client
 * 
 * This is a READ-ONLY Supabase client for the public website.
 * NO ADMIN FUNCTIONALITY OR SERVICE KEYS ARE EXPOSED.
 * Only customer-facing operations are allowed.
 */

import { createClient } from '@supabase/supabase-js';

// Environment validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing required Supabase environment variables for public client');
}

// Validate that no admin access is configured
if (process.env.NEXT_PUBLIC_ADMIN_ACCESS === 'true') {
  throw new Error('Admin access is not allowed in public subdomain');
}

// Default headers for public client
const DEFAULT_HEADERS = {
  'X-Client-Info': 'ocean-soul-sparkles-public@1.0.0',
  'X-Client-Type': 'public-website'
};

// Fetch with timeout for reliability
const fetchWithTimeout = (url, options = {}) => {
  const timeout = 10000; // 10 seconds
  
  return Promise.race([
    fetch(url, options),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Request timeout')), timeout)
    )
  ]);
};

/**
 * Create public Supabase client with restricted permissions
 */
function createPublicSupabaseClient() {
  try {
    console.log('[Public Supabase] Creating public client instance');
    
    const client = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: false,
        storageKey: 'oss_public_auth_token',
        storage: typeof window !== 'undefined' ? window.localStorage : undefined,
        cookieOptions: {
          path: '/',
          sameSite: 'Lax',
          secure: process.env.NODE_ENV === 'production'
        }
      },
      global: {
        headers: DEFAULT_HEADERS,
        fetch: fetchWithTimeout
      },
      realtime: {
        params: {
          eventsPerSecond: 1 // Limited for public use
        }
      }
    });

    console.log('[Public Supabase] Client created successfully');
    return client;
  } catch (error) {
    console.error('[Public Supabase] Error creating client:', error);
    throw error;
  }
}

/**
 * Public Supabase client instance
 * Only allows customer-facing operations
 */
export const supabase = createPublicSupabaseClient();

/**
 * Get current authenticated customer user
 * Only returns customer role users
 */
export async function getCurrentCustomer() {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return null;
    }

    // Verify this is a customer user (not admin/staff)
    const { data: roleData } = await supabase
      .from('user_roles')
      .select('role')
      .eq('id', user.id)
      .single();

    // Only allow customer role access
    if (roleData?.role !== 'user') {
      console.warn('[Public Supabase] Non-customer user attempted access:', roleData?.role);
      await supabase.auth.signOut();
      return null;
    }

    return user;
  } catch (error) {
    console.error('[Public Supabase] Error getting current customer:', error);
    return null;
  }
}

/**
 * Customer authentication functions
 */
export const customerAuth = {
  /**
   * Sign in customer
   */
  async signIn(email, password) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        throw error;
      }

      // Verify customer role
      const customer = await getCurrentCustomer();
      if (!customer) {
        throw new Error('Access denied: Customer account required');
      }

      return { data, error: null };
    } catch (error) {
      console.error('[Public Auth] Customer sign in error:', error);
      return { data: null, error };
    }
  },

  /**
   * Sign up customer
   */
  async signUp(email, password, metadata = {}) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            role: 'user', // Force customer role
            ...metadata
          }
        }
      });

      return { data, error };
    } catch (error) {
      console.error('[Public Auth] Customer sign up error:', error);
      return { data: null, error };
    }
  },

  /**
   * Sign out customer
   */
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      return { error };
    } catch (error) {
      console.error('[Public Auth] Customer sign out error:', error);
      return { error };
    }
  }
};

/**
 * Public data access functions
 */
export const publicData = {
  /**
   * Get active services for public booking
   */
  async getServices() {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('status', 'active')
        .eq('visible_on_public', true)
        .gte('duration', 120)
        .lte('duration', 360)
        .order('name');

      return { data, error };
    } catch (error) {
      console.error('[Public Data] Error fetching services:', error);
      return { data: null, error };
    }
  },

  /**
   * Get active products for public shop
   */
  async getProducts(category = null) {
    try {
      let query = supabase
        .from('products')
        .select('*')
        .eq('status', 'active');

      if (category && category !== 'all') {
        query = query.eq('category_name', category);
      }

      const { data, error } = await query.order('name');
      return { data, error };
    } catch (error) {
      console.error('[Public Data] Error fetching products:', error);
      return { data: null, error };
    }
  }
};

export default supabase;
