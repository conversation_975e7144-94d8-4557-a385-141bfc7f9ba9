import { useState, useEffect, useRef } from 'react';

export default function AnimatedSection({ 
  children, 
  animation = 'fade-in', 
  delay = 0,
  className = '',
  threshold = 0.1 
}) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setTimeout(() => {
            setIsVisible(true);
            setHasAnimated(true);
          }, delay);
        }
      },
      { threshold }
    );

    const currentElement = elementRef.current;
    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [delay, threshold, hasAnimated]);

  const getAnimationStyles = () => {
    const baseStyle = {
      transition: 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
    };

    switch (animation) {
      case 'fade-in':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateY(0)' : 'translateY(30px)'
        };
      
      case 'slide-left':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateX(0)' : 'translateX(-50px)'
        };
      
      case 'slide-right':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateX(0)' : 'translateX(50px)'
        };
      
      case 'slide-up':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateY(0)' : 'translateY(50px)'
        };
      
      case 'slide-down':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateY(0)' : 'translateY(-50px)'
        };
      
      case 'scale-in':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'scale(1)' : 'scale(0.8)'
        };
      
      case 'rotate-in':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'rotate(0deg) scale(1)' : 'rotate(-10deg) scale(0.8)'
        };
      
      case 'bounce-in':
        return {
          ...baseStyle,
          transition: isVisible 
            ? 'all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)' 
            : 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'scale(1)' : 'scale(0.3)'
        };
      
      default:
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0
        };
    }
  };

  return (
    <div
      ref={elementRef}
      className={className}
      style={getAnimationStyles()}
    >
      {children}
    </div>
  );
}
