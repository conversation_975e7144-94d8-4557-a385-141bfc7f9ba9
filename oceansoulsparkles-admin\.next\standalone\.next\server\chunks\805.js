"use strict";exports.id=805,exports.ids=[805],exports.modules={8781:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,a){return a in t?t[a]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,a)):"function"==typeof t&&"default"===a?t:void 0}}})},7474:(e,t,a)=>{a.d(t,{$g:()=>v,rE:()=>A,Wg:()=>p,cm:()=>I});var i=a(9344),r=a.n(i),s=a(8432),n=a.n(s),o=a(9200),c=a.n(o),l=a(2885);let d=(0,l.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function u(e){try{let t={action:e.action,user_id:e.userId,user_role:e.userRole,email:e.email,ip_address:e.ip,path:e.path,resource:e.resource,resource_id:e.resourceId,old_values:e.oldValues,new_values:e.newValues,reason:e.reason,error:e.error,metadata:e.metadata,severity:e.severity||"medium",created_at:new Date().toISOString()},{error:a}=await d.from("audit_logs").insert(t);a&&console.error("Failed to write audit log to database:",a);let i=function(e){switch(e){case"low":case"medium":default:return"log";case"high":return"warn";case"critical":return"error"}}(e.severity||"medium"),r=function(e){let t=new Date().toISOString(),a=e.userId?`[User: ${e.userId}]`:"",i=e.ip?`[IP: ${e.ip}]`:"",r=e.path?`[Path: ${e.path}]`:"";return`[AUDIT] ${t} ${e.action} ${a} ${i} ${r} ${e.reason||""}`.trim()}(e);console[i](r),"critical"===e.severity&&await m(e)}catch(t){console.error("Audit logging failed:",t),console.error("AUDIT_LOG_FAILURE:",JSON.stringify(e,null,2))}}async function m(e){try{console.error("\uD83D\uDEA8 CRITICAL SECURITY EVENT:",{action:e.action,userId:e.userId,ip:e.ip,reason:e.reason,timestamp:new Date().toISOString()}),"true"===process.env.ENABLE_CRITICAL_ALERTS&&await _(e)}catch(e){console.error("Failed to send critical alert:",e)}}async function _(e){console.log("Email alert would be sent for:",e.action)}let f=(0,l.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function p(e){try{let t=r().verify(e,process.env.JWT_SECRET),{data:a,error:i}=await f.from("admin_users").select(`
        id,
        email,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        last_activity,
        permissions
      `).eq("id",t.userId).eq("is_active",!0).single();if(i||!a)return{valid:!1,error:"User not found or inactive"};if(!a.is_active)return{valid:!1,error:"User account is deactivated"};return await f.from("admin_users").update({last_activity:new Date().toISOString()}).eq("id",a.id),{valid:!0,user:{id:a.id,email:a.email,role:a.role,firstName:a.first_name,lastName:a.last_name,isActive:a.is_active,mfaEnabled:a.mfa_enabled,lastActivity:Date.now(),permissions:a.permissions||[]}}}catch(e){return{valid:!1,error:"Invalid token"}}}async function v(e,t,a){try{let{data:i}=await f.from("login_attempts").select("*").eq("email",e).gte("created_at",new Date(Date.now()-9e5).toISOString()).order("created_at",{ascending:!1});if(i&&i.length>=5)return await u({action:"LOGIN_BLOCKED",email:e,ip:a,reason:"Too many failed attempts"}),{success:!1,error:"Account temporarily locked due to too many failed attempts"};let{data:s,error:o}=await f.from("admin_users").select(`
        id,
        email,
        password_hash,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        mfa_secret,
        permissions
      `).eq("email",e.toLowerCase()).single();if(o||!s)return await w(e,a,"User not found"),{success:!1,error:"Invalid credentials"};if(!s.is_active)return await u({action:"LOGIN_DENIED",userId:s.id,email:e,ip:a,reason:"Account deactivated"}),{success:!1,error:"Account is deactivated"};if(!await n().compare(t,s.password_hash))return await w(e,a,"Invalid password"),{success:!1,error:"Invalid credentials"};if(await f.from("login_attempts").delete().eq("email",e),s.mfa_enabled&&s.mfa_secret)return{success:!0,requiresMFA:!0,user:{id:s.id,email:s.email,role:s.role,firstName:s.first_name,lastName:s.last_name,isActive:s.is_active,mfaEnabled:s.mfa_enabled,lastActivity:Date.now(),permissions:s.permissions||[]}};let c=r().sign({userId:s.id,email:s.email,role:s.role},process.env.JWT_SECRET,{expiresIn:"8h"});return await f.from("admin_users").update({last_login:new Date().toISOString(),last_activity:new Date().toISOString()}).eq("id",s.id),await u({action:"LOGIN_SUCCESS",userId:s.id,userRole:s.role,email:e,ip:a}),{success:!0,token:c,user:{id:s.id,email:s.email,role:s.role,firstName:s.first_name,lastName:s.last_name,isActive:s.is_active,mfaEnabled:s.mfa_enabled,lastActivity:Date.now(),permissions:s.permissions||[]}}}catch(e){return console.error("Admin login error:",e),{success:!1,error:"Login failed"}}}async function I(e,t,a){try{let{data:i,error:s}=await f.from("admin_users").select(`
        id,
        email,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        mfa_secret,
        permissions
      `).eq("id",e).single();if(s||!i||!i.mfa_secret)return{success:!1,error:"Invalid MFA setup"};if(!c().totp.verify({secret:i.mfa_secret,encoding:"base32",token:t,window:2}))return await u({action:"MFA_FAILED",userId:i.id,email:i.email,ip:a,reason:"Invalid MFA token"}),{success:!1,error:"Invalid MFA token"};let n=r().sign({userId:i.id,email:i.email,role:i.role},process.env.JWT_SECRET,{expiresIn:"8h"});return await f.from("admin_users").update({last_login:new Date().toISOString(),last_activity:new Date().toISOString()}).eq("id",i.id),await u({action:"MFA_LOGIN_SUCCESS",userId:i.id,userRole:i.role,email:i.email,ip:a}),{success:!0,token:n,user:{id:i.id,email:i.email,role:i.role,firstName:i.first_name,lastName:i.last_name,isActive:i.is_active,mfaEnabled:i.mfa_enabled,lastActivity:Date.now(),permissions:i.permissions||[]}}}catch(e){return console.error("MFA verification error:",e),{success:!1,error:"MFA verification failed"}}}async function w(e,t,a){await f.from("login_attempts").insert({email:e,ip_address:t,success:!1,reason:a,created_at:new Date().toISOString()}),await u({action:"LOGIN_FAILED",email:e,ip:t,reason:a})}async function A(e,t){try{await u({action:"LOGOUT",userId:e,ip:t})}catch(e){console.error("Logout audit error:",e)}}},7153:(e,t)=>{var a;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return a}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(a||(a={}))},1802:(e,t,a)=>{e.exports=a(145)}};